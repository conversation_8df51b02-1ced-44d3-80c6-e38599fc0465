stages:
    - deploy
variables:
  GIT_DEPTH: 50
  
docker-build-token: 
    stage: deploy
    tags:
        - shell
    before_script:
        - sudo yum install -y libxml2
        - git clean -ffdx
        - git reset --hard
    script:
        - version=$(xmllint --xpath "//Version/text()" ./src/Coder.Token.WebApi/Coder.Token.WebApi.csproj 2>/dev/null)
        - echo "版本号 $version"
        - register=zhcoder-docker-registry.com:8000/coder
        - image="coder-token-webapi"
        - cd src
        - sudo docker buildx build --platform linux/amd64,linux/arm64 -t $register/$image:$version --push -f ./Coder.Token.WebApi/Dockerfile .
