dotnet build ./src/Coder.Token.Abstractions/Coder.Token.Abstractions.csproj --configuration Release
dotnet pack  ./src/Coder.Token.Abstractions/Coder.Token.Abstractions.csproj -o ./nuget -c Release

dotnet build ./src/Coder.Token.HttpClients/Coder.Token.HttpClients.csproj --configuration Release
dotnet pack  ./src/Coder.Token.HttpClients/Coder.Token.HttpClients.csproj -o ./nuget -c Release


dotnet build ./src/Coder.Authentication/Coder.Authentication.csproj --configuration Release
dotnet pack  ./src/Coder.Authentication/Coder.Authentication.csproj -o ./nuget -c Release


# Navigate to the nuget directory
Set-Location -Path ./nuget

# Push all NuGet packages
Get-ChildItem -Filter *.nupkg | ForEach-Object {
    dotnet nuget push $_.FullName -s "publish" --skip-duplicate
}