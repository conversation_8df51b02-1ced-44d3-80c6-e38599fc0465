﻿using Coder.Authentication.Authorize;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;

// ReSharper disable once CheckNamespace
namespace Coder.Authentication;

/// <summary>
/// </summary>
public class CoderAuthorizeAttribute : AuthorizeAttribute
{
    /// <summary>
    /// </summary>
    /// <param name="clients">"Token,会员系统"</param>
    /// <param name="orgs">组织单元,如 "审计,销售"</param>
    /// <param name="users">用户</param>
    /// <param name="roles"></param>
    public CoderAuthorizeAttribute(string clients = null, string orgs = null, string users = null,
        string roles = null)
    {
        var authorizeData = new CoderAuthorizeData
        {
            Clients = clients?.Trim(),
            Roles = roles?.Trim(),
            Users = users?.Trim(),
            Orgs = orgs?.Trim(),
            AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme
        };
        Policy = CoderAuthorizePolicyProvider.PolicyPrefix + JsonConvert.SerializeObject(authorizeData);
    }
}