﻿using Coder.Authentication.Authorize;

// ReSharper disable once CheckNamespace
namespace Coder.Authentication;

/// <summary>
/// </summary>
public class CoderAuthorizeData : ICoderAuthorizeData
{
    /// <summary>
    /// 
    /// </summary>
    public string Policy { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string Roles { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string AuthenticationSchemes { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string Clients { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string Orgs { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string Users { get; set; }
}