﻿using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;

namespace Coder.Authentication.Authorize;

/// <summary>
/// </summary>
public class CoderAuthorizeHandler : AuthorizationHandler<PermissionAuthorizationRequirement>
{
    /// 参考文章 https://learn.microsoft.com/zh-cn/aspnet/core/security/authorization/iard?view=aspnetcore-8.0  还是用 AuthroizeAttribute
    /// https://github.com/dotnet/aspnetcore/tree/v3.1.3/src/Security/samples/CustomPolicyProvider 源码
    /// 
    /// https://blog.tubumu.com/2019/11/06/aspnetcore-extend-authorization-new/ 扩展出 新标签
    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="requirement"></param>
    /// <returns></returns>
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context,
        PermissionAuthorizationRequirement requirement)
    {
        if (context.User.Identity == null || !context.User.Identity.IsAuthenticated)
            return Task.CompletedTask;

        var result = false;

        if (!string.IsNullOrWhiteSpace(requirement.AuthorizeData.Users))
        {
            var users = requirement.AuthorizeData.Users.Split(',');
            result = users.Contains(context.User.Identity?.Name);
        }


        result = result || (!string.IsNullOrWhiteSpace(requirement.AuthorizeData.Roles) &&
                            MatchClaims(context, ClaimTypes.Role, requirement.AuthorizeData.Roles));


        result = result || (!string.IsNullOrWhiteSpace(requirement.AuthorizeData.Orgs) &&
                            MatchOrgClient(context, requirement.AuthorizeData.Orgs));


        //aud 就是客户端，能被那些微服务访问
        result = result || (!string.IsNullOrWhiteSpace(requirement.AuthorizeData.Clients) &&
                            MatchClaims(context, "aud", requirement.AuthorizeData.Clients));


        if (result) context.Succeed(requirement);

        return Task.CompletedTask;
    }

    private bool MatchClaims(AuthorizationHandlerContext context, string type, string matchValue)
    {
        if (matchValue.Trim() == "*") return context.User.Claims.Any(claim => claim.Type == type && claim.Value != "api");

        var matcher = matchValue.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(item => item.Trim());
        var result = context.User.Claims.Any(claim => claim.Type == type && matcher.Contains(claim.Value));

        return result;
    }

    private bool MatchOrgClient(AuthorizationHandlerContext context, string orgsString)
    {
        var matcher = orgsString.Split(",");
        var userOrgs = context.User.Claims.Where(claim => claim.Type == "org").Select(item => item.Value.Trim());
        if (!userOrgs.Any()) return false;
        foreach (var userOrg in userOrgs)
            foreach (var orgString in matcher)
                if (orgString == userOrg || orgString.StartsWith(userOrg))
                    return true;

        return false;
    }
}