﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Coder.Authentication.Authorize;

/// <summary>
/// 
/// </summary>
public class CoderAuthorizePolicyProvider : IAuthorizationPolicyProvider
{
    internal const string PolicyPrefix = "CoderAuth:";
    private readonly DefaultAuthorizationPolicyProvider _fallbackPolicyProvider;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="options"></param>
    public CoderAuthorizePolicyProvider(IOptions<AuthorizationOptions> options)
    {
        _fallbackPolicyProvider = new DefaultAuthorizationPolicyProvider(options);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="policyName"></param>
    /// <returns></returns>
    public Task<AuthorizationPolicy> GetPolicyAsync(string policyName)
    {
        if (policyName.StartsWith(PolicyPrefix, StringComparison.OrdinalIgnoreCase))
        {
            var policyValue = policyName.Substring(PolicyPrefix.Length);
            var authorizeData = JsonConvert.DeserializeObject<CoderAuthorizeData>(policyValue);
            var policy = new AuthorizationPolicyBuilder();
            policy.AddRequirements(new PermissionAuthorizationRequirement(authorizeData));
            return Task.FromResult(policy.Build());
        }

        // If the policy name doesn't match the format expected by this policy provider,
        // try the fallback provider. If no fallback provider is used, this would return 
        // Task.FromResult<AuthorizationPolicy>(null) instead.
        return _fallbackPolicyProvider.GetPolicyAsync(policyName);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public Task<AuthorizationPolicy> GetDefaultPolicyAsync()
    {
        return _fallbackPolicyProvider.GetDefaultPolicyAsync();
    }
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public Task<AuthorizationPolicy> GetFallbackPolicyAsync()
    {
        return _fallbackPolicyProvider.GetFallbackPolicyAsync();
    }
}