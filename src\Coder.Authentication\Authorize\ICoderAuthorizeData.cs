﻿using Microsoft.AspNetCore.Authorization;

namespace Coder.Authentication.Authorize;

/// <summary>
/// </summary>
public interface ICoderAuthorizeData : IAuthorizeData
{
    /// <summary>
    /// 微服务，用“,"隔开
    /// </summary>
    string Clients { get; set; }

    /// <summary>
    /// 组织单元 微服务，用“,"隔开
    /// </summary>
    public string Orgs { get; set; }
    /// <summary>
    /// 用户 微服务，用“,"隔开
    /// </summary>
    public string Users { get; set; }
}