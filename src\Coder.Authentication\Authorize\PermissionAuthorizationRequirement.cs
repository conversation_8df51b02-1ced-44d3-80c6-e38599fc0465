﻿using Microsoft.AspNetCore.Authorization;

namespace Coder.Authentication.Authorize;

/// <summary>
/// 
/// </summary>
public class PermissionAuthorizationRequirement : IAuthorizationRequirement
{
    /// <summary>
    /// 
    /// </summary>
    public CoderAuthorizeData AuthorizeData { get; }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="authorizeData"></param>
    public PermissionAuthorizationRequirement(CoderAuthorizeData authorizeData)
    {
        AuthorizeData = authorizeData;
    }
   
}