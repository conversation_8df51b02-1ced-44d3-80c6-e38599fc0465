﻿using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;

namespace Coder.Authentication.Cache;

/// <summary>
/// </summary>
public class TokenCacheItem
{
    /// <summary>
    /// </summary>
    public string[] Audience { get; set; }

    /// <summary>
    /// </summary>
    public bool IsAuthed { get; set; }
}

/// <summary>
///     存储已经验证过的Token的缓存，用于加快验证速度。
/// </summary>
public class TokenVerifiedCache
{
    private readonly IDistributedCache _cache;

    /// <summary>
    /// </summary>
    public TokenVerifiedCache()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="memoryCache"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public TokenVerifiedCache(IDistributedCache memoryCache)
    {
        _cache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
    }

    /// <summary>
    /// </summary>
    /// <param name="tokenSignature"></param>
    /// <param name="hasAuthenticated"></param>
    /// <param name="audiences"></param>
    /// <param name="cacheExpire"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void SetBySignature([NotNull] string tokenSignature, bool hasAuthenticated,
        [MaybeNull] string[] audiences = null, [MaybeNull] DateTimeOffset? cacheExpire = null)
    {
        if (string.IsNullOrEmpty(tokenSignature)) throw new ArgumentNullException(nameof(tokenSignature));
        TimeSpan expire;
        if (cacheExpire != null)
        {
            var remind = (cacheExpire.Value - DateTimeOffset.Now).TotalMinutes;
            if (remind < 0)
                remind = 30;
            expire = TimeSpan.FromMinutes(remind);
        }
        else
        {
            expire = hasAuthenticated ? TimeSpan.FromMinutes(10) : TimeSpan.FromMinutes(2);
        }

        var storeItem = new TokenCacheItem
        {
            IsAuthed = hasAuthenticated,
            Audience = audiences
        };
        //验证失败，保存为null
        var ary = JsonConvert.SerializeObject(storeItem);


        _cache.SetString(tokenSignature, ary, new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expire
        });
    }


    /// <summary>
    ///     token是否正确
    /// </summary>
    /// <param name="token"></param>
    /// <param name="audiences"></param>
    /// <param name="hasAuthenticated"></param>
    /// <returns>是否再cache存在</returns>
    public bool TryGetValidated([NotNull] string token, out string[] audiences, out bool hasAuthenticated)
    {
        if (token == null) throw new ArgumentNullException(nameof(token));
        audiences = null;


        hasAuthenticated = false;
        var signature = Utility.Signature(token);
        var cacheString = _cache.GetString(signature);
        if (cacheString == null) return false;
        try
        {
            var cacheItem = JsonConvert.DeserializeObject<TokenCacheItem>(cacheString);

            hasAuthenticated = cacheItem.IsAuthed;


            audiences = cacheItem.Audience;
            return true;
        }
        catch
        {
            return false;
        }
    }
}