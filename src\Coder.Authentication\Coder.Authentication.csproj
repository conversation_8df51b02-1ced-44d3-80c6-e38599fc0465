<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net8.0;net9.0</TargetFrameworks>
		<Authors>珠海酷迪技术有限公司</Authors>
		<Product>珠海酷迪技术有限公司-微服务-用户鉴权</Product>
		<Description>增加验证header</Description>
		<Version>9.1.5</Version>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>Coder.Authentication.xml</DocumentationFile>
	</PropertyGroup>
	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Coder.Utility" Version="1.2.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
	</ItemGroup>


	<ItemGroup Condition=" '$(TargetFramework)' == 'net8.0'">
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer">
			<Version>8.0.18</Version>
		</PackageReference>
	</ItemGroup>
	<ItemGroup Condition=" '$(TargetFramework)' == 'net9.0'">
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer">
			<Version>9.0.0</Version>
		</PackageReference>
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\Coder.Token.Abstractions\Coder.Token.Abstractions.csproj" />
	  <ProjectReference Include="..\Coder.Token.HttpClients\Coder.Token.HttpClients.csproj" />
	</ItemGroup>
	
</Project>