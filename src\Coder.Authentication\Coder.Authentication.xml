<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Coder.Authentication</name>
    </assembly>
    <members>
        <member name="T:Coder.Authentication.CoderAuthorizeAttribute">
            <summary>
            </summary>
        </member>
        <member name="M:Coder.Authentication.CoderAuthorizeAttribute.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            </summary>
            <param name="clients">"Token,会员系统"</param>
            <param name="orgs">组织单元,如 "审计,销售"</param>
            <param name="users">用户</param>
            <param name="roles"></param>
        </member>
        <member name="T:Coder.Authentication.CoderAuthorizeData">
            <summary>
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthorizeData.Policy">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthorizeData.Roles">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthorizeData.AuthenticationSchemes">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthorizeData.Clients">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthorizeData.Orgs">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthorizeData.Users">
            <summary>
            
            </summary>
        </member>
        <member name="T:Coder.Authentication.Authorize.CoderAuthorizeHandler">
            <summary>
            </summary>
        </member>
        <member name="M:Coder.Authentication.Authorize.CoderAuthorizeHandler.HandleRequirementAsync(Microsoft.AspNetCore.Authorization.AuthorizationHandlerContext,Coder.Authentication.Authorize.PermissionAuthorizationRequirement)">
            参考文章 https://learn.microsoft.com/zh-cn/aspnet/core/security/authorization/iard?view=aspnetcore-8.0  还是用 AuthroizeAttribute
            https://github.com/dotnet/aspnetcore/tree/v3.1.3/src/Security/samples/CustomPolicyProvider 源码
            
            https://blog.tubumu.com/2019/11/06/aspnetcore-extend-authorization-new/ 扩展出 新标签
            <summary>
            </summary>
            <param name="context"></param>
            <param name="requirement"></param>
            <returns></returns>
        </member>
        <member name="T:Coder.Authentication.Authorize.CoderAuthorizePolicyProvider">
            <summary>
            
            </summary>
        </member>
        <member name="M:Coder.Authentication.Authorize.CoderAuthorizePolicyProvider.#ctor(Microsoft.Extensions.Options.IOptions{Microsoft.AspNetCore.Authorization.AuthorizationOptions})">
            <summary>
            
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:Coder.Authentication.Authorize.CoderAuthorizePolicyProvider.GetPolicyAsync(System.String)">
            <summary>
            
            </summary>
            <param name="policyName"></param>
            <returns></returns>
        </member>
        <member name="M:Coder.Authentication.Authorize.CoderAuthorizePolicyProvider.GetDefaultPolicyAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Coder.Authentication.Authorize.CoderAuthorizePolicyProvider.GetFallbackPolicyAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Coder.Authentication.Authorize.ICoderAuthorizeData">
            <summary>
            </summary>
        </member>
        <member name="P:Coder.Authentication.Authorize.ICoderAuthorizeData.Clients">
            <summary>
            微服务，用“,"隔开
            </summary>
        </member>
        <member name="P:Coder.Authentication.Authorize.ICoderAuthorizeData.Orgs">
            <summary>
            组织单元 微服务，用“,"隔开
            </summary>
        </member>
        <member name="P:Coder.Authentication.Authorize.ICoderAuthorizeData.Users">
            <summary>
            用户 微服务，用“,"隔开
            </summary>
        </member>
        <member name="T:Coder.Authentication.Authorize.PermissionAuthorizationRequirement">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.Authorize.PermissionAuthorizationRequirement.AuthorizeData">
            <summary>
            
            </summary>
        </member>
        <member name="M:Coder.Authentication.Authorize.PermissionAuthorizationRequirement.#ctor(Coder.Authentication.CoderAuthorizeData)">
            <summary>
            
            </summary>
            <param name="authorizeData"></param>
        </member>
        <member name="T:Coder.Authentication.Cache.TokenCacheItem">
            <summary>
            </summary>
        </member>
        <member name="P:Coder.Authentication.Cache.TokenCacheItem.Audience">
            <summary>
            </summary>
        </member>
        <member name="P:Coder.Authentication.Cache.TokenCacheItem.IsAuthed">
            <summary>
            </summary>
        </member>
        <member name="T:Coder.Authentication.Cache.TokenVerifiedCache">
            <summary>
                存储已经验证过的Token的缓存，用于加快验证速度。
            </summary>
        </member>
        <member name="M:Coder.Authentication.Cache.TokenVerifiedCache.#ctor">
            <summary>
            </summary>
        </member>
        <member name="M:Coder.Authentication.Cache.TokenVerifiedCache.#ctor(Microsoft.Extensions.Caching.Distributed.IDistributedCache)">
            <summary>
            </summary>
            <param name="memoryCache"></param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Coder.Authentication.Cache.TokenVerifiedCache.SetBySignature(System.String,System.Boolean,System.String[],System.Nullable{System.DateTimeOffset})">
            <summary>
            </summary>
            <param name="tokenSignature"></param>
            <param name="hasAuthenticated"></param>
            <param name="audiences"></param>
            <param name="cacheExpire"></param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Coder.Authentication.Cache.TokenVerifiedCache.TryGetValidated(System.String,System.String[]@,System.Boolean@)">
            <summary>
                token是否正确
            </summary>
            <param name="token"></param>
            <param name="audiences"></param>
            <param name="hasAuthenticated"></param>
            <returns>是否再cache存在</returns>
        </member>
        <member name="T:Coder.Authentication.CoderAuthenticationDefaults">
            <summary>
            </summary>
        </member>
        <member name="F:Coder.Authentication.CoderAuthenticationDefaults.Schema">
            <summary>
                默认Coder
            </summary>
        </member>
        <member name="T:Coder.Authentication.CoderAuthenticationOptions">
            <summary>
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.Host">
            <summary>
                服务器
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.Client">
            <summary>
                发起请求的应用id
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.SecretKey">
            <summary>
                与Client一起访问TokenBuilding 接口，获取token/ticket等信息。
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.ClientToken">
            <summary>
                一个用于服务之间访问的token。
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.IssuerSigningKey">
            <summary>
                用于验证jwt的key。
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.Audiences">
            <summary>
            验证的用户。只有远程获取aud才会
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.RedisConnection">
            <summary>
            Redis 的连接串。
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.ValidateAudience">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.ValidateIssuerSigningKey">
            <summary>
            
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.EnableDynamicKeyUpdate">
            <summary>
            启用动态密钥热更新
            </summary>
        </member>
        <member name="P:Coder.Authentication.CoderAuthenticationOptions.KeyRefreshIntervalMinutes">
            <summary>
            密钥刷新间隔（分钟），默认5分钟
            </summary>
        </member>
        <member name="T:Coder.Authentication.CoderTokenException">
            <summary>
            </summary>
        </member>
        <member name="M:Coder.Authentication.CoderTokenException.#ctor(System.String)">
            <summary>
            </summary>
            <param name="message"></param>
        </member>
        <member name="T:Coder.Authentication.JwtBuilderExtensions">
            <summary>
            </summary>
        </member>
        <member name="M:Coder.Authentication.JwtBuilderExtensions.AddCoderJwtAuth(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Coder.Authentication.CoderAuthenticationOptions})">
            <summary>
            </summary>
            <param name="services"></param>
            <param name="settingAction"></param>
            <returns></returns>
        </member>
        <member name="T:Coder.Authentication.JwtKeyRefreshService">
            <summary>
                JWT密钥刷新后台服务
            </summary>
        </member>
        <member name="M:Coder.Authentication.JwtKeyRefreshService.#ctor(Microsoft.Extensions.Logging.ILogger{Coder.Authentication.JwtKeyRefreshService},System.IServiceProvider,Coder.Authentication.CoderAuthenticationOptions)">
            <summary>
            
            </summary>
            <param name="logger"></param>
            <param name="serviceProvider"></param>
            <param name="options"></param>
        </member>
        <member name="M:Coder.Authentication.JwtKeyRefreshService.Dispose">
            <summary>
            
            </summary>
        </member>
        <member name="M:Coder.Authentication.JwtKeyRefreshService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Coder.Authentication.JwtKeyRefreshService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
    </members>
</doc>
