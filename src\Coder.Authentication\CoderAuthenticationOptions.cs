﻿using Microsoft.AspNetCore.Authentication;

namespace Coder.Authentication;

/// <summary>
/// </summary>
public class CoderAuthenticationOptions : AuthenticationSchemeOptions
{
    private string _host;

    /// <summary>
    ///     服务器
    /// </summary>
    public string Host
    {
        get => _host;
        set => _host = value.TrimEnd('/');
    }


    /// <summary>
    ///     发起请求的应用id
    /// </summary>
    public string Client { get; set; }

    /// <summary>
    ///     与Client一起访问TokenBuilding 接口，获取token/ticket等信息。
    /// </summary>
    public string SecretKey { get; set; }


    /// <summary>
    ///     一个用于服务之间访问的token。
    /// </summary>
    public string ClientToken { get; set; }

    /// <summary>
    ///     用于验证jwt的key。
    /// </summary>
    public string IssuerSigningKey { get; set; }
    /// <summary>
    /// 验证的用户。只有远程获取aud才会
    /// </summary>
    public string[] Audiences { get; set; }
    /// <summary>
    /// Redis 的连接串。
    /// </summary>
    public string RedisConnection { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool ValidateAudience { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    public bool ValidateIssuerSigningKey { get; set; } = true;

    /// <summary>
    /// 启用动态密钥热更新
    /// </summary>
    public bool EnableDynamicKeyUpdate { get; set; } = true;

    /// <summary>
    /// 密钥刷新间隔（分钟），默认5分钟
    /// </summary>
    public int KeyRefreshIntervalMinutes { get; set; } = 5;

}