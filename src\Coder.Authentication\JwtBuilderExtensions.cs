﻿using System;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using Coder.Authentication.Authorize;
using Coder.Authentication.Cache;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using StackExchange.Redis;

namespace Coder.Authentication;

/// <summary>
/// </summary>
public static class JwtBuilderExtensions
{
    private static RSA GetRemoteConfiguration(Action<CoderAuthenticationOptions> settingAction,
        CoderAuthenticationOptions option)
    {
        settingAction(option);
        var rsa = RSA.Create();

        // 注意: JWT密钥现在由JwtKeyRefreshService后台服务获取和刷新
        // 如果密钥为空，等待后台服务获取，最多等待30秒
        var waitTime = 0;
        const int maxWaitTime = 30000; // 30秒
        const int waitInterval = 500; // 500毫秒

        while (string.IsNullOrWhiteSpace(option.IssuerSigningKey) && waitTime < maxWaitTime)
        {
            Thread.Sleep(waitInterval);
            waitTime += waitInterval;
        }

        if (string.IsNullOrWhiteSpace(option.IssuerSigningKey))
            throw new Exception("JWT密钥获取超时，请检查JwtKeyRefreshService后台服务是否正常运行。");

        rsa.ImportFromPem(option.IssuerSigningKey.ToCharArray());
        return rsa;
    }

    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    /// <param name="settingAction"></param>
    /// <returns></returns>
    public static IServiceCollection AddCoderJwtAuth(this IServiceCollection services,
        Action<CoderAuthenticationOptions> settingAction)
    {
        var option = new CoderAuthenticationOptions();
        settingAction(option);
        if (!string.IsNullOrEmpty(option.RedisConnection))
            services.AddStackExchangeRedisCache(action =>
                {
                    action.Configuration = option.RedisConnection;
                });

        services.AddSingleton(option).AddSingleton<TokenVerifiedCache>();


        // 注册JWT密钥刷新后台服务
        services.AddHostedService<JwtKeyRefreshService>();

        services.AddSingleton<IAuthorizationHandler, CoderAuthorizeHandler>()
            .AddSingleton<IAuthorizationPolicyProvider, CoderAuthorizePolicyProvider>();

        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(jwtBearerOptions =>
            {
                var token = new TokenValidationParameters
                {
                    // IssuerSigningKey = new RsaSecurityKey(rsa),
                    ValidateAudience = option.ValidateAudience, // 开启 Audience 验证
                    ValidateIssuerSigningKey = option.ValidateIssuerSigningKey,
                    ValidateIssuer = false,
                    IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
                    {
                        var rsa1 = GetRemoteConfiguration(settingAction, option);
                        return new[] { new RsaSecurityKey(rsa1) };
                    }
                };

                token.ValidAudience = option.Client;

                jwtBearerOptions.TokenValidationParameters = token;
                jwtBearerOptions.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Query["access_token"];

                        // If the request is for our hub...
                        //var path = context.HttpContext.Request.Path;

                        // Read the token out of the query string
                        context.Token = accessToken;

                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        if (!string.IsNullOrWhiteSpace(option.RedisConnection))

                            try
                            {
                                var tokenBlacklistService = context.HttpContext.RequestServices
                                    .GetService<TokenVerifiedCache>();
                                if (tokenBlacklistService == null) return Task.CompletedTask;

                                var token = context.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                                tokenBlacklistService.TryGetValidated(token, out _, out var hasAuthenticated);

                                if (!hasAuthenticated) context.Fail("Token is blacklisted");
                            }
                            catch (RedisConnectionException ex)
                            {
                                var loggerFactory = context.HttpContext.RequestServices
                                    .GetService<ILoggerFactory>();
                                var logger = loggerFactory?.CreateLogger("JwtAuthentication");
                                logger?.LogWarning(ex, "Redis连接失败，黑名单功能不可用");
                            }

                        return Task.CompletedTask;
                    }
                };
            });

        return services;
    }
}