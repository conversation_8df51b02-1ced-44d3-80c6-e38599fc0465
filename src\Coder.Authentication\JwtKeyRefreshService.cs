using System;
using System.Threading;
using System.Threading.Tasks;
using Coder.Token.Clients;
using Coder.Token.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Coder.Authentication;

/// <summary>
///     JWT密钥刷新后台服务
/// </summary>
public class JwtKeyRefreshService : IHostedService, IDisposable
{
    private readonly ILogger<JwtKeyRefreshService> _logger;
    private readonly CoderAuthenticationOptions _options;
    private readonly IServiceProvider _serviceProvider;
    private Timer? _timer;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="serviceProvider"></param>
    /// <param name="options"></param>
    public JwtKeyRefreshService(
        ILogger<JwtKeyRefreshService> logger,
        IServiceProvider serviceProvider,
        CoderAuthenticationOptions options)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _options = options;
    }
    /// <summary>
    /// 
    /// </summary>
    public void Dispose()
    {
        _timer?.Dispose();
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("JWT密钥刷新服务启动");

        // 立即执行一次获取密钥
        //await RefreshJwtKeyAsync();

        // 设置定时器，每30分钟刷新一次
        _timer = new Timer(async _ => await RefreshJwtKeyAsync(),
            null,
            TimeSpan.Zero,
            TimeSpan.FromMinutes(30));
        return Task.CompletedTask;
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("JWT密钥刷新服务停止");
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    private async Task RefreshJwtKeyAsync()
    {
        try
        {
            _logger.LogDebug("开始刷新JWT密钥");

            if (string.IsNullOrWhiteSpace(_options.IssuerSigningKey))
            {
                var maxTry = 10;
                var isSuccess = false;

                for (var i = 0; i < maxTry; i++)
                    try
                    {
                        var clientInfo = await GetClientInfoAsync(_options);

                        _options.Audiences = clientInfo.Data.Audience;
                        _options.IssuerSigningKey = clientInfo.Data.JwtSecretKey;
                        _options.ClientToken = clientInfo.Data.Token;
                        _options.RedisConnection = clientInfo.Data.RedisConnection;

                        isSuccess = true;
                        _logger.LogInformation("JWT密钥刷新成功");
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "第{Attempt}次获取JWT密钥失败，{Delay}毫秒后重试", i + 1, 1000 + 500 * (i + 1));
                        await Task.Delay(1000 + 500 * (i + 1));
                    }

                if (!isSuccess)
                {
                    _logger.LogError("联系Token服务器失败，已达到最大重试次数");
                    throw new Exception("联系Token服务器失败。");
                }
            }

            if (string.IsNullOrWhiteSpace(_options.IssuerSigningKey))
            {
                _logger.LogError("JWT密钥为空");
                throw new Exception("联系Token服务器失败。");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新JWT密钥时发生异常");
        }
    }

    private async Task<TokenResult<ClientInfoResult>> GetClientInfoAsync(CoderAuthenticationOptions options)
    {
        var submit = new ClientInfoSubmit
        {
            Client = options.Client,
            ExpireMinute = 60 * 24 * 30
        };

        var client = _serviceProvider.GetRequiredService<ITokenBuilderClient>();

        return await client.GetClientInfo(submit);
    }
}