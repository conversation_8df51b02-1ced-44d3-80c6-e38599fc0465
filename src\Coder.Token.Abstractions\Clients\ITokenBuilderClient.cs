﻿using System.Threading.Tasks;
using Coder.Authentication;
using Coder.Token.ViewModels;

namespace Coder.Token.Clients;

public interface ITokenBuilderClient
{
    /// <summary>
    ///     客户端名称
    /// </summary>
    string Client { get; set; }

    /// <summary>
    ///     密钥
    /// </summary>
    string SecretKey { get; set; }

    /// <summary>
    ///     创建token
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<TokenResult<CreateTokenResult>> CreateTokenAsync(CreateTokenSubmit submit);

    /// <summary>
    /// 创建换取token的 ticket
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<TokenResult<CreateTokenTicketResult>> CreateTicketAsync(CreateTokenSubmit submit);

    /// <summary>
    /// </summary>
    /// <param name="submitTicket"></param>
    /// <returns></returns>
    Task<TokenResult<CreateTokenResult>> CreateTokenByTicketAsync(string submitTicket);
    /// <summary>
    /// 
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<TokenResult<ClientInfoResult>> GetClientInfo(ClientInfoSubmit submit);
    /// <summary>
    /// 移除token
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<TokenResult> ExpireTokenAsync(RemoveUserTokenSubmit submit);


}