﻿namespace Coder.Token.Clients;

public class TokenResult
{
    public TokenResult()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="success"></param>
    public TokenResult(string message, int code)
    {
        Message = message;
        Code = code;
    }

    /// <summary>
    /// </summary>
    public bool Success => Code == 0;

    /// <summary>
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// </summary>
    public int Code { get; set; }
}

/// <summary>
/// </summary>
public class TokenResult<T> : TokenResult
{
    /// <summary>
    /// </summary>
    public TokenResult()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="success"></param>
    public TokenResult(string message, int code) : base(message, code)
    {
    }


    /// <summary>
    /// </summary>
    public T Data { get; set; }
}