<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net8.0;net9.0;</TargetFrameworks>
		<RootNamespace>Coder.Token</RootNamespace>
		<Version>9.2.0</Version>
		<Authors>珠海市技术有限公司</Authors>
		<Product>Coder.Token.Service</Product>
		<PackageTags>Coder.Token.Service,token</PackageTags>
	</PropertyGroup>

	<ItemGroup Condition=" '$(TargetFramework)' == 'net8.0'">
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
	</ItemGroup>
	<ItemGroup Condition=" '$(TargetFramework)' == 'net9.0'">
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
	</ItemGroup>
	
</Project>