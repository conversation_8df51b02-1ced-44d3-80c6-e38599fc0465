﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;

namespace Coder.Token;

/// <summary>
/// </summary>
internal class CoderJwtSecurityTokenHandler : JwtSecurityTokenHandler
{
    /// <summary>
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    public ClaimsIdentity CreateByTokenWithoutVerify(string token)
    {
        var jwtSecurityToken = ReadJwtToken(token);
        var result = CreateClaimsIdentity(jwtSecurityToken, jwtSecurityToken.Issuer,
            new TokenValidationParameters());
        foreach (var auds in jwtSecurityToken.Audiences)
        {
            if(auds.ToLower()=="api")continue;
            result.AddClaim(new Claim("aud", auds));
        }

        return result;
    }
}