﻿using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;

namespace Coder.Token;

public static class JwtValidator
{
    private static readonly CoderJwtSecurityTokenHandler Handler = new();

    /// <summary>
    ///     检查token是否合法。只检查
    /// </summary>
    /// <param name="token"></param>
    /// <param name="issue"></param>
    /// <param name="jwtToken"></param>
    /// <returns></returns>
    public static bool TryValidateJwtToken(string token,SecurityKey securityKey, string issue, out JwtSecurityToken jwtToken)
    {
        if (token == null) throw new ArgumentNullException(nameof(token));
       
        jwtToken = null;
        var tokenHandler = new JwtSecurityTokenHandler();

        try
        {
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidIssuer = issue,

                ValidateIssuerSigningKey = true,
                IssuerSigningKey = securityKey,
                ValidateIssuer = true, //签发需要验证。
                ValidateAudience = false,
                ValidateLifetime = true,
                // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
                ClockSkew = TimeSpan.Zero
            }, out var validatedToken);

            jwtToken = (JwtSecurityToken)validatedToken;
            return true;
        }
        catch (SecurityTokenException)
        {
            // JWT安全token异常，token无效
            return false;
        }
        catch (ArgumentException)
        {
            // 参数异常，token格式错误
            return false;
        }
        catch (Exception)
        {
            // 其他未预期的异常也返回false，但在生产环境中应该记录日志
            return false;
        }
    }

    /// <summary>
    ///     通过token获取ClaimsIdentity信息，并且没有没有验证token是否正确。
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    public static ClaimsIdentity GetJwtTokeFrom(string token)
    {
        return Handler.CreateByTokenWithoutVerify(token);
    }

    /// <summary>
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    private static JwtSecurityToken GetJwtSecurityToken(string token)
    {
        return Handler.ReadJwtToken(token);
    }
}