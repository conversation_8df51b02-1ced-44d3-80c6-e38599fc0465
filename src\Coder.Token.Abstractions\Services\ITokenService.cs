﻿using System;
using System.Threading.Tasks;
using Coder.Authentication;
using Coder.Token.Clients;
using Coder.Token.ViewModels;

namespace Coder.Token.Services;

public interface ITokenService
{
    /// <summary>
    ///     验证Token
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    Task<TokenValidateResult> VerifyAsync(string token);

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<BuildTokenResult> BuildTokenAsync(BuildUserTokenSubmit submit);

    /// <summary>
    ///     根据Ticket 创建 Token
    /// </summary>
    /// <param name="ticket"></param>
    /// <returns></returns>
    Task<BuildTokenResult> BuildTokenByTicketAsync(string ticket);


    /// <summary>
    ///     为客户端生成token，用于api-gateway的验证。
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<BuildTokenResult> BuildClientTokenAsync(BuildClientTokeSubmit submit);


    /// <summary>
    ///     移除登录的token，如果token没有超时，那么会暂时放在黑名单内，直到超时。
    /// </summary>
    /// <param name="removeToken">jtw-token</param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    Task<TokenResult> RemoveTokenAsync(RemoveTokenSubmit removeToken);

    /// <summary>
    ///     创建token的ticket，通过这个ticket可以规定时间内换取token一次。
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    Task<BuildTokenTicketResult> BuildTokenTicketAsync(BuildUserTokenSubmit submit);
}