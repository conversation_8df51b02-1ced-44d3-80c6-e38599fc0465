﻿using System;


// Re<PERSON><PERSON>per disable once CheckNamespace
namespace Coder.Authentication;

/// <summary>
///     创建token的结果
/// </summary>
public class BuildTokenResult
{
    /// <summary>
    ///     获取Token
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    ///     被访问
    /// </summary>
    public string Audience { get; set; }

    /// <summary>
    /// </summary>
    public DateTimeOffset ExpireTime { get; set; }
}

public class ClientInfoResult
{
    /// <summary>
    ///     获取Token
    /// </summary>
    public string Token { get; set; }

    public string[] Audience { get; set; }

    public string Issuer { get; set; }

    /// <summary>
    /// redis 连接
    /// </summary>
    public string RedisConnection { get; set; }
    /// <summary>
    /// 公钥
    /// </summary>
    public string JwtSecretKey { get; set; }
    /// <summary>
    /// </summary>
    public DateTimeOffset ExpireTime { get; set; }
}