﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Coder.Token.ViewModels;

// ReSharper disable once CheckNamespace
namespace Coder.Authentication;

/// <summary>
///     创建TokeSubmit
/// </summary>
public class BuildUserTokenSubmit : BuildTokenSubmitBase
{

    /// <summary>
    /// </summary>
    public IEnumerable<ClaimSubmit> Claims { get; set; }

    /// <summary>
    ///     设置超时
    /// </summary>
    public int? ExpireMinutes { get; set; }
}

public class BuildUserTicketSubmit : BuildUserTokenSubmit
{


}

public class BuildClientTicketSubmit : BuildUserTokenSubmit
{
    [Required]
    public string RequireAccessService { get; set; }
}