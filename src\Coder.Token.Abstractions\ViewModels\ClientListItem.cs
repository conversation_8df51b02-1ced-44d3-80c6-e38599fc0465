﻿using System.Collections.Generic;

namespace Coder.Token.ViewModels;

public class ClientListItem
{
    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public IList<string> Services { get; set; } = new List<string>();

    /// <summary>
    /// </summary>
    public string SecretKey { get; set; }

    /// <summary>
    /// </summary>
    public string Description { get; set; }
}