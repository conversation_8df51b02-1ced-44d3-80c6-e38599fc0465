﻿using System.Collections.Generic;

namespace Coder.Token.ViewModels;
/// <summary>
/// 
/// </summary>
public class ClientSubmit
{
    /// <summary>
    /// 
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public string SecretKey { get; set; }

    public IList<string> Services { get; set; }


    public ClaimBuildType ClaimBuildType { get; set; }



}