﻿using Coder.Authentication;

namespace Coder.Token.ViewModels;

public class ClientTokeSubmit : BuildTokenSubmitBase
{
    /// <summary>
    ///     请求访问的服务
    /// </summary>
    public string RequestAccessService { get; set; }

    /// <summary>
    ///     过期
    /// </summary>
    public int? ExpireMinute { get; set; }
}



public class ClientInfoSubmit : BuildTokenSubmitBase
{

    /// <summary>
    ///     过期
    /// </summary>
    public int? ExpireMinute { get; set; }
}