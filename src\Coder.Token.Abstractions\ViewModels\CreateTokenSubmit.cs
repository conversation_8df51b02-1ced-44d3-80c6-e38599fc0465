﻿using System.Collections.Generic;
using System.Security.Claims;
using Coder.Token.ViewModels;

namespace Coder.Authentication;

public class CreateTokenSubmit : BuildTokenSubmitBase
{
    /// <summary>
    /// </summary>
    public IEnumerable<ClaimSubmit> Claims { get; set; }

    /// <summary>
    ///     设置超时
    /// </summary>
    public int? ExpireMinutes { get; set; }

    
}

public class CreateTokenTicketResult
{
    public string Ticket { get; set; }
}