﻿using System;

namespace Coder.Token;

/// <summary>
///     Token 黑名单
/// </summary>
public class BlackListToken
{
    public BlackListToken()
    {
    }

    public BlackListToken(string token, DateTime tokenExpireTime, string userName)
    {
        if (token == null) throw new ArgumentNullException(nameof(token));
        UserName = userName ?? throw new ArgumentNullException(nameof(userName));
        TokenSignature = Utility.Signature(token);
        MoveOutTime = tokenExpireTime;
    }

    /// <summary>
    ///     id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     token
    /// </summary>
    public string TokenSignature { get; set; }

    /// <summary>
    ///     可以移出黑名单的时间段。
    /// </summary>
    public DateTime MoveOutTime { get; set; }

    /// <summary>
    /// </summary>
    public string UserName { get; set; }
}