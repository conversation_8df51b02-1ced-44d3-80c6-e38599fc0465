﻿using System.Collections.Generic;

namespace Coder.Token;

/// <summary>
///     授权人。接入验证系统的订阅系统
/// </summary>
public class Client
{
    /// <summary>
    ///     client的id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     client名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     加密串
    /// </summary>
    public string SecretKey { get; set; }

    /// <summary>
    ///     d
    /// </summary>
    public IList<string> Services { get; set; } = new List<string>();

    /// <summary>
    ///     是否可以由客户端提交声明，默认false，
    /// </summary>
    public ClaimBuildType ClaimBuildType { get; set; } = ClaimBuildType.Client;
}

