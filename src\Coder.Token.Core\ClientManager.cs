﻿using System;
using Coder.Token.Stores;
using Coder.Token.TokenCache;
using Coder.Token.ViewModels;

namespace Coder.Token;

/// <summary>
/// </summary>
public class ClientManager
{
    private readonly ClientCache _cache;
    private readonly IClientStore _store;

    /// <summary>
    /// </summary>
    /// <param name="store"></param>
    /// <param name="cache"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public ClientManager(IClientStore store, ClientCache cache)
    {
        _store = store ?? throw new ArgumentNullException(nameof(store));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
    }


    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public Client Get(string key)
    {
        if (key == null) throw new ArgumentNullException(nameof(key));
        var result = _cache.GetByName(key);
        if (result != null)
            return result;

        result = _store.Get(key);
        if (result == null)
            return null;
        _cache.SetClient(result);
        return result;
    }
}