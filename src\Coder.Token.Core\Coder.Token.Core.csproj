<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net9.0</TargetFrameworks>
		<IsPackable>true</IsPackable>
		<Version>9.2.0</Version>
		<Authors>珠海酷迪技术有限公司</Authors>
		<Company>珠海酷迪技术有限公司</Company>
		<Product>Coder.Token.Service</Product>
		<PackageTags>Coder.Token.Service,token</PackageTags>
		<RootNamespace>Coder.Token</RootNamespace>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Coder.WebRequestVerifier" Version="2.0.2" />
	
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
		<PackageReference Include="OptimizedPriorityQueue" Version="5.1.0" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Coder.Token.Abstractions\Coder.Token.Abstractions.csproj" />
	</ItemGroup>
</Project>