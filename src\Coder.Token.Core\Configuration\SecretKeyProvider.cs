using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;

namespace Coder.Token.Configuration;

/// <summary>
/// 密钥提供器，用于安全获取客户端密钥
/// </summary>
public interface ISecretKeyProvider
{
    /// <summary>
    /// 获取客户端密钥
    /// </summary>
    /// <param name="clientName">客户端名称</param>
    /// <returns>密钥</returns>
    string GetSecretKey(string clientName);
}

/// <summary>
/// 基于环境变量和配置的密钥提供器
/// </summary>
public class EnvironmentSecretKeyProvider : ISecretKeyProvider
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EnvironmentSecretKeyProvider> _logger;

    public EnvironmentSecretKeyProvider(
        IConfiguration configuration,
        ILogger<EnvironmentSecretKeyProvider> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public string GetSecretKey(string clientName)
    {
        if (string.IsNullOrEmpty(clientName))
            throw new ArgumentNullException(nameof(clientName));

        // 优先从环境变量获取
        var envKey = $"CLIENT_SECRET_{clientName.ToUpperInvariant()}";
        var secretKey = Environment.GetEnvironmentVariable(envKey);
        
        if (!string.IsNullOrEmpty(secretKey))
        {
            _logger.LogDebug("从环境变量 {EnvKey} 获取客户端 {ClientName} 的密钥", envKey, clientName);
            return secretKey;
        }

        // 从配置文件获取
        var configKey = $"ClientSecrets:{clientName}";
        secretKey = _configuration[configKey];
        
        if (!string.IsNullOrEmpty(secretKey))
        {
            _logger.LogDebug("从配置文件获取客户端 {ClientName} 的密钥", clientName);
            return secretKey;
        }

        // 从客户端配置中获取（向后兼容，但会记录警告）
        var clientConfigKey = $"TokenService:Clients:{clientName}:SecretKey";
        secretKey = _configuration[clientConfigKey];
        
        if (!string.IsNullOrEmpty(secretKey))
        {
            _logger.LogWarning("客户端 {ClientName} 正在使用配置文件中的密钥，建议迁移到环境变量", clientName);
            return secretKey;
        }

        _logger.LogError("未找到客户端 {ClientName} 的密钥配置", clientName);
        throw new InvalidOperationException($"未找到客户端 {clientName} 的密钥配置");
    }
}