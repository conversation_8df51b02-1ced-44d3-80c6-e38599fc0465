using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Coder.Token.Configuration;

/// <summary>
/// Token服务配置
/// </summary>
public class TokenServiceConfiguration
{
    /// <summary>
    /// JWT令牌颁发者
    /// </summary>
    [Required]
    public string Issuer { get; set; } = string.Empty;

    /// <summary>
    /// 默认过期时间（分钟）
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "ExpireMinutes必须大于0")]
    public int ExpireMinutes { get; set; } = 30;

    /// <summary>
    /// 票据过期时间（分钟）
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "TicketExpireMinutes必须大于0")]
    public int TicketExpireMinutes { get; set; } = 3;

    /// <summary>
    /// 票据最大使用次数
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "MaxTicketUseTimes必须大于0")]
    public int MaxTicketUseTimes { get; set; } = 3;

    /// <summary>
    /// 客户端配置
    /// </summary>
    public Dictionary<string, ClientConfiguration> Clients { get; set; } = new();
}

/// <summary>
/// 客户端配置
/// </summary>
public class ClientConfiguration
{
    /// <summary>
    /// 客户端名称
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 客户端描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 密钥（应从环境变量或密钥管理服务获取）
    /// </summary>
    [Required]
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// 服务权限列表
    /// </summary>
    public List<string> Services { get; set; } = new();

    /// <summary>
    /// 声明构建类型
    /// </summary>
    public ClaimBuildType ClaimBuildType { get; set; } = ClaimBuildType.Client;
}