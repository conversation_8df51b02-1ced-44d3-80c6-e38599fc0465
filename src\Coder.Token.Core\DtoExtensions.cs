﻿using System.Collections.Generic;
using Coder.Token.ViewModels;

namespace Coder.Token;

public static class DtoExtensions
{
    public static ClientListItem ToViewModel(this Client client)
    {
        return new ClientListItem
        {
            Id = client.Id,
            Name = client.Name,
            Services = client.Services ?? new List<string>(),
            SecretKey = client.SecretKey,
            Description = client.Description
        };
    }

    public static void FillTo(this ClientSubmit submit, Client client)
    {
        client.Description = submit.Description;
        client.SecretKey = submit.SecretKey;
        client.Name = submit.Name;
        client.Services = submit.Services ?? new List<string>();
        client.ClaimBuildType = submit.ClaimBuildType;
    }
}