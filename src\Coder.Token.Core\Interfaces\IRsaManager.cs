using System;
using System.Security.Cryptography;

namespace Coder.Token.Interfaces;

/// <summary>
/// RSA密钥管理器接口
/// </summary>
public interface IRsaManager : IDisposable
{
    /// <summary>
    /// 获取RSA实例
    /// </summary>
    /// <returns>RSA实例</returns>
    RSA GetRsa();

    /// <summary>
    /// 获取私钥的PEM格式字符串
    /// </summary>
    /// <returns>私钥PEM字符串</returns>
    string GetPrivateKeyPem();

    /// <summary>
    /// 获取公钥的PEM格式字符串
    /// </summary>
    /// <returns>公钥PEM字符串</returns>
    string GetPublicKeyPem();

    /// <summary>
    /// 获取私钥参数
    /// </summary>
    /// <returns>RSA参数</returns>
    RSAParameters GetPrivateKeyParameters();

    /// <summary>
    /// 获取公钥参数
    /// </summary>
    /// <returns>RSA参数</returns>
    RSAParameters GetPublicKeyParameters();

    /// <summary>
    /// 重新生成密钥对
    /// </summary>
    void RegenerateKeyPair();
}