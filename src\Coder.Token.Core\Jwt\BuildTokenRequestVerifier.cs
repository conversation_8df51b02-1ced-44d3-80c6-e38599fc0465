﻿using System;
using System.Security.Cryptography;
using System.Text;
using Coder.Authentication;

using Coder.WebRequestVerifier;
using Coder.WebRequestVerifier.Verifiers;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Coder.Token.Jwt;

/// <summary>
/// </summary>
/// <exception cref="NotFoundAudienceException">找不到订阅者信息</exception>
public class BuildTokenRequestVerifier<TBuildTokenSubmitBase> : IVerifier, ISignature
    where TBuildTokenSubmitBase : BuildTokenSubmitBase
{
    private readonly ILogger<BuildTokenRequestVerifier<TBuildTokenSubmitBase>> _logger;
    private readonly ClientManager _store;

    public BuildTokenRequestVerifier(ClientManager store,
        ILogger<BuildTokenRequestVerifier<TBuildTokenSubmitBase>> logger)
    {
        _store = store ?? throw new ArgumentNullException(nameof(store));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public string Sign(byte[] body)
    {
        if (body == null) throw new ArgumentNullException(nameof(body));
        var json = Encoding.UTF8.GetString(body);
        var submit = JsonConvert.DeserializeObject<TBuildTokenSubmitBase>(json);
        if (submit == null)
        {
            throw new ArgumentNullException(nameof(submit), "submit.client是必填。");
        }
        var audience = _store.Get(submit.Client);
        if (audience == null) throw new NotFoundAudienceException(submit.Client);
        var signatureResult = submit + audience.SecretKey;
        _logger.LogDebug("验证签名内容" + signatureResult);
        using var md5 = MD5.Create();
        return md5.ComputeHash(Encoding.UTF8.GetBytes(signatureResult)).ToHexString();
    }

    public void Dispose()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="requestBody"></param>
    /// <param name="signature"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public bool Verify(string requestBody, string signature, out string errorMessage)
    {
        if (requestBody == null) throw new ArgumentNullException(nameof(requestBody));
        if (signature == null) throw new ArgumentNullException(nameof(signature));
        if (_logger.IsEnabled(LogLevel.Debug))
            _logger.LogDebug("获取验证的body为：" + requestBody + "签名：" + signature);

        errorMessage = null;
        var submit = JsonConvert.DeserializeObject<TBuildTokenSubmitBase>(requestBody);
        var audience = _store.Get(submit.Client);
        if (audience == null)
        {
            errorMessage = $"没有找到{submit.Client}的设置";
            _logger.LogWarning(errorMessage);

            return false;
        }

        var signatureStr = requestBody + audience.SecretKey;
        _logger.LogDebug("验证签名内容" + signatureStr);

        using var md5 = MD5.Create();
        var signatureResult = md5.ComputeHash(Encoding.UTF8.GetBytes(signatureStr));


        var result = signatureResult.ToHexString() == signature.ToUpper();
        if (!result)
        {
            errorMessage = "签名验证失败。";
            _logger.LogInformation("验证失败。签名结果:" + signatureResult.ToHexString() + "，提交的签名");
        }

        
        return result;
    }
}