﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Coder.Token.Interfaces;
using Microsoft.Extensions.Logging;

namespace Coder.Token
{
    /// <summary>
    /// RSA管理器，用于管理RSA私钥和生成公钥
    /// </summary>
    public class RsaManager : IRsaManager
    {
        private const string RsaDirectory = "rsa";
        private const string PrivateKeyFile = "rsa/private.pem";
        private const string PublicKeyFile = "rsa/public.pem";
        
        private RSA _rsa;
        private readonly object _lock = new object();
        private readonly ILogger<RsaManager> _logger;

        public RsaManager(ILogger<RsaManager> logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取RSA实例，如果私钥文件不存在则自动创建
        /// </summary>
        public RSA GetRsa()
        {
            if (_rsa != null)
                return _rsa;

            lock (_lock)
            {
                if (_rsa != null)
                    return _rsa;

                _rsa = RSA.Create();
                
                // 确保RSA目录存在
                if (!Directory.Exists(RsaDirectory))
                {
                    Directory.CreateDirectory(RsaDirectory);
                }

                // 如果私钥文件存在，则导入私钥
                if (File.Exists(PrivateKeyFile))
                {
                    LoadPrivateKey();
                }
                else
                {
                    // 如果私钥文件不存在，则生成新的RSA密钥对
                    GenerateKeyPair();
                }

                return _rsa;
            }
        }

        /// <summary>
        /// 获取私钥的PEM格式字符串
        /// </summary>
        /// <returns>私钥PEM字符串</returns>
        public string GetPrivateKeyPem()
        {
            var rsa = GetRsa();
            return rsa.ExportRSAPrivateKeyPem();
        }

        /// <summary>
        /// 获取公钥的PEM格式字符串
        /// </summary>
        /// <returns>公钥PEM字符串</returns>
        public string GetPublicKeyPem()
        {
            var rsa = GetRsa();
            return rsa.ExportRSAPublicKeyPem();
        }

        /// <summary>
        /// 获取私钥参数
        /// </summary>
        /// <returns>RSA参数</returns>
        public RSAParameters GetPrivateKeyParameters()
        {
            var rsa = GetRsa();
            return rsa.ExportParameters(true);
        }

        /// <summary>
        /// 获取公钥参数
        /// </summary>
        /// <returns>RSA参数</returns>
        public RSAParameters GetPublicKeyParameters()
        {
            var rsa = GetRsa();
            return rsa.ExportParameters(false);
        }

        /// <summary>
        /// 重新生成密钥对
        /// </summary>
        public void RegenerateKeyPair()
        {
            lock (_lock)
            {
                _rsa?.Dispose();
                _rsa = RSA.Create();
                GenerateKeyPair();
            }
        }

        /// <summary>
        /// 从文件加载私钥
        /// </summary>
        private void LoadPrivateKey()
        {
            try
            {
                string privateKeyText = File.ReadAllText(PrivateKeyFile, Encoding.UTF8);
                _rsa.ImportFromPem(privateKeyText.ToCharArray());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "加载私钥文件失败: {PrivateKeyFile}", PrivateKeyFile);
                throw new InvalidOperationException($"加载私钥文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成新的密钥对并保存到文件
        /// </summary>
        private void GenerateKeyPair()
        {
            try
            {
                // 生成2048位的RSA密钥对
                _rsa = RSA.Create(2048);
                
                // 导出私钥并保存到文件
                string privateKeyPem = _rsa.ExportRSAPrivateKeyPem();
                File.WriteAllText(PrivateKeyFile, privateKeyPem, Encoding.UTF8);
                
                // 导出公钥并保存到文件
                string publicKeyPem = _rsa.ExportRSAPublicKeyPem();
                File.WriteAllText(PublicKeyFile, publicKeyPem, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "生成RSA密钥对失败");
                throw new InvalidOperationException($"生成RSA密钥对失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _rsa?.Dispose();
        }
    }
}
