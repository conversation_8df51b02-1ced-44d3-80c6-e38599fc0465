﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Coder.Token.ViewModels;

namespace Coder.Token.Stores;

public interface IClientStore
{
    Task<Client?> GetByIdAsync(int id);
    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<IEnumerable<Client>> ListAsync(ClientSearcher searcher);

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    void AddOrUpdate(Client client);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangeAsync();

    /// <summary>
    /// </summary>
    /// <param name="clientName"></param>
    /// <returns></returns>
    [return: MaybeNull]
    Client Get(string clientName);
    /// <summary>
    /// </summary>
    /// <param name="clientName"></param>
    /// <returns></returns>

    Task<Client?> GetAsync(string clientName);
    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="excludeId"></param>
    /// <returns></returns>
    bool Exist(string name, int? excludeId);

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    void Delete(string name);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<int> CountAsync(ClientSearcher searcher);

    /// <summary>
    /// </summary>
    /// <param name="submitId"></param>
    /// <returns></returns>
    Client? GetById(int submitId);
}