﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coder.Token.Stores;

/// <summary>
/// </summary>
public interface ITokenBlackListStore
{
    /// <summary>
    /// </summary>
    /// <param name="blackListToken"></param>
    void AddOrUpdate(BlackListToken blackListToken);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangeAsync();

    /// <summary>
    /// </summary>
    /// <param name="top"></param>
    /// <param name="now"></param>
    /// <returns></returns>
    IEnumerable<BlackListToken> GetEffectBlackList(int top, DateTime now);
}