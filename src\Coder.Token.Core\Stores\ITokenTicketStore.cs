﻿using System.Threading;
using System.Threading.Tasks;

namespace Coder.Token.Stores;

/// <summary>
/// </summary>
public interface ITokenTicketStore
{
    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    void AddOrUpdate(TokenTicket client);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// </summary>
    /// <param name="ticket"></param>
    /// <returns></returns>
    Task<TokenTicket> GetByEffectAsync(string ticket);
}