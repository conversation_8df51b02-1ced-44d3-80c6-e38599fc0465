﻿using Coder.Token.Clients;

namespace Coder.Token;

public static class TokeResultHelper
{
    public static TokenResult<T> ToSuccessTokenResult<T>(this T data, string message)
    {
        return new TokenResult<T>(message, 0)
        {
            Data = data
        };
    }

    public static TokenResult<T> ToErrorTokenResult<T>(this T o, string errorMessage, int code)
    {
        return new TokenResult<T>(errorMessage, code)
        {
            Data = o,
        };
    }
}