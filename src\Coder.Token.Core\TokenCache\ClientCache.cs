﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;

namespace Coder.Token.TokenCache;

public class ClientCache
{
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _defaultCacheDuration;

    /// <summary>
    /// </summary>
    /// <param name="cache"></param>
    /// <param name="cacheDurationSeconds">缓存持续时间（秒），默认60秒</param>
    public ClientCache(IMemoryCache cache, int cacheDurationSeconds = 60)
    {
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _defaultCacheDuration = TimeSpan.FromSeconds(cacheDurationSeconds);
    }

    public virtual Client GetByName(string clientName)
    {
        if (string.IsNullOrEmpty(clientName))
            return null;

        return _cache.TryGetValue(clientName, out var client) ? client as Client : null;
    }

    public virtual void SetClient(Client client, TimeSpan? expiration = null)
    {
        ArgumentNullException.ThrowIfNull(client);

        if (string.IsNullOrEmpty(client.Name))
            throw new ArgumentException("客户端名称不能为空", nameof(client));

        var cacheExpiration = expiration ?? _defaultCacheDuration;
        _cache.Set(client.Name, client, cacheExpiration);
    }

    public virtual void RemoveClient(string clientName)
    {
        if (!string.IsNullOrEmpty(clientName))
        {
            _cache.Remove(clientName);
        }
    }

    public virtual async Task<Client> GetOrSetAsync(string clientName, Func<Task<Client>> factory, TimeSpan? expiration = null)
    {
        var cached = GetByName(clientName);
        if (cached != null)
            return cached;

        var client = await factory().ConfigureAwait(false);
        if (client != null)
        {
            SetClient(client, expiration);
        }
        return client;
    }
}