using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.Authentication;
using Coder.Token.Clients;
using Coder.Token.Interfaces;
using Coder.Token.Stores;
using Coder.Token.ViewModels;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Coder.Token;

public class TokenManager
{
    private readonly IClientStore _clientStore;

    private readonly ILogger<TokenManager> _logger;
    private readonly TokenServiceOption _option;
    private readonly IRsaManager _rsaManager;
    private readonly ITokenTicketStore _tokenTicketStore;


    /// <summary>
    /// </summary>
    /// <param name="option"></param>
    /// <param name="tokenTicketStore"></param>
    /// <param name="clientStore"></param>
    /// <param name="logger"></param>
    public TokenManager(TokenServiceOption option,
        ITokenTicketStore tokenTicketStore, IClientStore clientStore,
        ILogger<TokenManager> logger, IRsaManager rsaManager)
    {
        if (option == null) throw new ArgumentNullException(nameof(option));

        if (option.ExpireMinutes <= 0)
            throw new ArgumentOutOfRangeException(nameof(option), "ExpireMinutes请输入大于0的jwt-token超时时间");


        _option = option;
        _tokenTicketStore = tokenTicketStore;
        _clientStore = clientStore;
        _logger = logger;
        _rsaManager = rsaManager;
    }

    private TokenResult<CreateTokenResult> GetClaims(Client client, CreateTokenSubmit submit,
        out List<ClaimSubmit> claims)
    {
        claims = new List<ClaimSubmit>();

        switch (client.ClaimBuildType)
        {
            case ClaimBuildType.Customs:
                if (submit.Claims == null || !submit.Claims.Any())
                    return new TokenResult<CreateTokenResult>
                    {
                        Data = new CreateTokenResult
                        {
                            Success = false,
                            Message = $"{submit.Client}只允许使用自定义声明，所以无法创建token。"
                        }
                    };
                break;
            case ClaimBuildType.Client:
                //client只能设置 客户端的时候，如果提交claims 报错。
                if (submit.Claims != null && submit.Claims.Any())
                    return new TokenResult<CreateTokenResult>
                    {
                        Data = new CreateTokenResult
                        {
                            Success = false,
                            Message = $"{submit.Client}不允许使用自定义声明，所以无法创建token。"
                        }
                    };

                break;
        }


        if (submit.Claims != null && submit.Claims.Any())
            claims.AddRange(submit.Claims);
        else
            claims =
            [
                new ClaimSubmit(ClaimTypes.Name, client.Name),
                new ClaimSubmit(ClaimTypes.NameIdentifier, client.Name),
                new ClaimSubmit("unique_name", client.Name)
            ];

        return new TokenResult<CreateTokenResult>
        {
            Data = new CreateTokenResult
            {
                Success = true,
                Message = "成功创建"
            }
        };
    }

    public Task<TokenResult<CreateTokenResult>> CreateTokenAsync(CreateTokenSubmit submit)
    {
        var client = _clientStore.Get(submit.Client);
        if (client == null)
            throw new ArgumentOutOfRangeException(nameof(submit), $"客户端 {submit.Client} 不存在");

        var result = GetClaims(client, submit, out var claims);
        if (result.Data.Success == false) return Task.FromResult(result);

        var autdience = new HashSet<string>(client.Services);
        //只有自定义claims，就需要 增加 autdicencs "User" 这个client
        if (submit.Claims != null && submit.Claims.Any())
            autdience.Add("User");

        var createResult = CreateJwtToken(
            claims,
            autdience.ToArray(),
            Math.Min(submit.ExpireMinutes ?? _option.ExpireMinutes, _option.ExpireMinutes)
        );

        return Task.FromResult(createResult.ToSuccessTokenResult("生成token成功。"));
    }

    public async Task<TokenResult<ClientInfoResult>> GetClientInfo(ClientInfoSubmit submit)
    {
        var data = new ClientInfoResult();
        var result = data.ToSuccessTokenResult("创建客户端Token成功。");
        var client = await _clientStore.GetAsync(submit.Client);

        if (client == null)
        {
            result.Message = "没有定义权限。";
            result.Code = -1;
            return result;
        }

        List<ClaimSubmit> claims =
        [
            new ClaimSubmit(ClaimTypes.Name, client.Name),
            new ClaimSubmit(ClaimTypes.NameIdentifier, client.Name),
            new ClaimSubmit("unique_name", client.Name)
        ];

        var tokenInfo = CreateJwtToken(
            claims,
            client.Services,
            submit.ExpireMinute ?? 43200);

        result.Data = new ClientInfoResult
        {
            Issuer = _option.Issuer,
            Audience = client.Services.ToArray(),
            ExpireTime = tokenInfo.ExpireTime,
            Token = tokenInfo.Token,
            JwtSecretKey = _rsaManager.GetPublicKeyPem()
        };

        return result;
    }

    private CreateTokenResult CreateJwtToken(IEnumerable<ClaimSubmit> claimsSubmit, IList<string> audiences,
        int expireMinute)
    {
        var expireTime = DateTimeOffset.Now.AddMinutes(expireMinute);
        var securityKey = new RsaSecurityKey(_rsaManager.GetRsa());
        var tokenDescriptor =
            new SecurityTokenDescriptor
            {
                Issuer = _option.Issuer,
                Expires = expireTime.DateTime,
                IssuedAt = DateTime.Now,
                SigningCredentials =
                    new SigningCredentials(securityKey, SecurityAlgorithms.RsaSha256)
            };
        if (audiences != null)
            foreach (var aud in audiences)
                tokenDescriptor.Audiences.Add(aud);


        IList<Claim> claims = new List<Claim>();
        if (claimsSubmit != null)
            foreach (var role in claimsSubmit)
                claims.Add(new Claim(role.Type, role.Value));

        tokenDescriptor.Subject = new ClaimsIdentity(claims.ToArray());
        var tokenHandler = new JwtSecurityTokenHandler();
        var securityToken = tokenHandler.CreateToken(tokenDescriptor);
        var token = tokenHandler.WriteToken(securityToken);

        return new CreateTokenResult
        {
            ExpireTime = expireTime,
            Token = token
        };
    }

    public async Task<TokenResult<CreateTokenTicketResult>> CreateTicketAsync(CreateTokenSubmit submit)
    {
        if (submit == null) throw new ArgumentNullException(nameof(submit));
        var client = await _clientStore.GetAsync(submit.Client);
        if (client == null)
        {
            return new TokenResult<CreateTokenTicketResult>("创建失效", 404);
        }
        var token = new TokenTicket();
        token.ExpireTime = DateTimeOffset.Now.AddMinutes(_option.TicketExpireMinutes);
        token.Status = TokenTicketStatus.Wait;
        token.Ticket = Guid.NewGuid().ToString("N");
        token.Remind = _option.MaxTicketUseTimes;
        token.Claims = submit.Claims;
        token.ExpireMinutes = submit.ExpireMinutes;
        token.Audience = string.Join(',', client.Services);

        _tokenTicketStore.AddOrUpdate(token);
        await _tokenTicketStore.SaveChangeAsync();
        return new TokenResult<CreateTokenTicketResult>
        {
            Data = new CreateTokenTicketResult
            {
                Ticket = token.Ticket
            }
        };
    }

    public async Task<TokenResult<CreateTokenResult>> CreateTokenByTicketAsync(string submitTicket)
    {
        if (submitTicket == null) throw new ArgumentNullException(nameof(submitTicket));
        var ticket = await _tokenTicketStore.GetByEffectAsync(submitTicket);
        if (ticket == null || ticket.Remind == 0)
            return new CreateTokenResult().ToErrorTokenResult("票据失效，请重新获取", -1);
        ticket.Remind--;

        if (ticket.Remind == 0)
            ticket.Status = TokenTicketStatus.Ineffective;

        _tokenTicketStore.AddOrUpdate(ticket);
        await _tokenTicketStore.SaveChangeAsync();

        var tokeResult = CreateJwtToken(ticket.Claims, ticket.Audience.Split(','),
            Math.Min(ticket.ExpireMinutes ?? _option.ExpireMinutes, _option.ExpireMinutes));

        return tokeResult.ToSuccessTokenResult("生成token成功。");
    }
}