using System;
using Coder.Authentication;
using Coder.Token.Interfaces;
using Coder.Token.Jwt;
using Coder.Token.TokenCache;
using Coder.Token.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.Token;

public static class TokenServiceExtensions
{
    /// <summary>
    ///     增加TokenManager，用于创建token
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddTokenService(this IServiceCollection services,
        Action<TokenServiceOption> optonAction, bool isDebug = false)
    {
        //if (expireMinutes <= 0)
        //    throw new ArgumentOutOfRangeException(nameof(expireMinutes), "ExpireMinutes时间不能为负数或者0");


        var option = new TokenServiceOption(services);
        optonAction(option);

        return services
                .AddSingleton(option)
                .AddMemoryCache()
                .AddSingleton<TokenVerifiedCache>()
                .AddSingleton<IRsaManager,RsaManager>()
                .AddSingleton<ClientCache>()
                .AddScoped<TokenValidateManager>()
                .AddScoped<TokenManager>()
                .AddScoped<ClientManager>()
                .AddTransient<BuildTokenRequestVerifier<BuildUserTicketSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<BuildUserTokenSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<RemoveUserTokenSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<ClientTokeSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<BuildClientTicketSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<RemoveClientTokenSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<ClientInfoSubmit>>()
                .AddTransient<BuildTokenRequestVerifier<CreateTokenSubmit>>()
            ;
    }
}