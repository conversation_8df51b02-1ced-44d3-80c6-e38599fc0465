﻿using System;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.Token;
/// <summary>
/// 
/// </summary>
public class TokenServiceOption
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="services"></param>
    public TokenServiceOption(IServiceCollection services)
    {
        Services = services;
    }

    /// <summary>
    /// </summary>
    public IServiceCollection Services { get; protected set; }


    /// <summary>
    /// </summary>
    public string Issuer { get; set; } = "CoderTokenService";

    /// <summary>
    /// token 默认超时时间
    /// </summary>
    public int ExpireMinutes { get; set; } = 720;
    /// <summary>
    /// Redis 配置
    /// </summary>
    public string RedisConnection { get; set; }
    /// <summary>
    /// Ticket 能够被使用的次数
    /// </summary>
    public int MaxTicketUseTimes
    {
        get;
        set;
    } = 2;

    public int TicketExpireMinutes { get; set; } = 3;
}