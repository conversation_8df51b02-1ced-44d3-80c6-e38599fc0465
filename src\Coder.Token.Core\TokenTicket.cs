﻿using System;
using System.Collections.Generic;
using Coder.Token.ViewModels;

namespace Coder.Token;

public class TokenTicket
{
    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public string Ticket { get; set; }

    /// <summary>
    ///     ticket在这时间之后失效
    /// </summary>
    public DateTimeOffset ExpireTime { get; set; }

    /// <summary>
    /// </summary>
    public TokenTicketStatus Status { get; set; }

    /// <summary>
    /// </summary>
    public IEnumerable<ClaimSubmit> Claims { get; set; }
    /// <summary>
    /// 剩下多少验证次数
    /// </summary>
    public int Remind { get; set; }

    /// <summary>
    /// 生成之后的token。
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    ///     订阅者
    /// </summary>
    public string Audience { get; set; }

    /// <summary>
    ///     创建Token的超时时间
    /// </summary>
    public int? ExpireMinutes { get; set; }
}