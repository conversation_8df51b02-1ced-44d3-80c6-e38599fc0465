﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.Token.Stores;
using Coder.Token.TokenCache;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coder.Token;

/// <summary>
/// </summary>
/// <remarks>
///     Manager 采用两种模式进行保存
/// </remarks>
public class TokenValidateManager
{
    private readonly IClientStore _clientStore;
    private readonly TokenServiceOption _option;
    private readonly IServiceProvider _provider;
    private readonly TokenVerifiedCache _tokenVerifiedCache;


    public TokenValidateManager(IServiceProvider provider, TokenVerifiedCache tokenVerifiedCache,
        IClientStore clientStore,
        TokenServiceOption option)
    {
        _provider = provider;
        _tokenVerifiedCache = tokenVerifiedCache;
        _clientStore = clientStore;

        _option = option;
        InitBlackList();
    }

    private void InitBlackList()
    {
        var scope = _provider.CreateScope();
        try
        {
            var blackListStore = scope.ServiceProvider.GetRequiredService<ITokenBlackListStore>();
            var result = blackListStore.GetEffectBlackList(1000, DateTime.Now);
            foreach (var tokenBlackListItem in result)
                _tokenVerifiedCache
                    .SetBySignature(tokenBlackListItem.TokenSignature, false,
                        cacheExpire: tokenBlackListItem.MoveOutTime);
        }
        catch (Exception ex)
        {
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<TokenValidateManager>>();
            logger.LogError(ex, "初始化黑名单出现错误。");
        }
        finally
        {
            scope.Dispose();
        }
    }


    public async Task SetToBlackListAsync(string token, string userName, DateTimeOffset expireTime)
    {
        if (token == null) throw new ArgumentNullException(nameof(token));

        _tokenVerifiedCache.Set(token, false, null, expireTime);

        using var scope = _provider.CreateScope();
        var blackListStore = scope.ServiceProvider.GetRequiredService<ITokenBlackListStore>();
        var key = new BlackListToken
        {
            TokenSignature = Utility.Signature(token),
            UserName = userName,
            MoveOutTime = expireTime.ToLocalTime().DateTime
        };

        blackListStore.AddOrUpdate(key);
        await blackListStore.SaveChangeAsync();
    }

    // 保留同步版本以向后兼容，但标记为过时
    [Obsolete("请使用 SetToBlackListAsync 方法，这个同步方法可能导致死锁")]
    public void SetToBlackList(string token, string userName, DateTimeOffset expireTime)
    {
        SetToBlackListAsync(token, userName, expireTime).GetAwaiter().GetResult();
    }

    public static DateTimeOffset GetExpireTime(IEnumerable<Claim> claims)
    {
        var expireTimeClaim = claims.FirstOrDefault(_ => _.Type == "exp");
        if (expireTimeClaim == null) return DateTimeOffset.Now.AddMinutes(30);

        return DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt32(expireTimeClaim.Value));
    }

    public static string GetUserName(IEnumerable<Claim> claims)
    {
        var result = claims.Where(_ => _.Type == ClaimTypes.NameIdentifier).ToArray();
        if (result.Any()) return result.First().Value;

        return null;
    }
}