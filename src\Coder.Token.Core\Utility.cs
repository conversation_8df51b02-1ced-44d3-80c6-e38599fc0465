﻿using System.Security.Cryptography;
using System.Text;

using Coder.WebRequestVerifier.Verifiers;

namespace Coder.Token;

internal class Utility
{
    public static string Signature(string token)
    {
        using var md5 = MD5.Create();
        return md5.ComputeHash(Encoding.UTF8.GetBytes(token)).ToHexString();
    }

    public static byte[] CombineByte(byte[] data1, byte[] data2)
    {
        var result = new byte[data1.Length + data2.Length];
        data1.CopyTo(result, 0);
        data2.CopyTo(result, data1.Length);
        return result;
    }

    public static bool IsEqual(byte[] data1, byte[] data2)
    {
        if (data1.Length != data2.Length)
            return false;

        for (var i = 0; i < data2.Length; i++)
            if (data1[i] != data2[i])
                return false;
        return true;
    }
}