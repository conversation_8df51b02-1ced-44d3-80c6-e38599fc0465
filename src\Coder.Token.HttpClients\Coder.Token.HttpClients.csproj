<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net8.0;net9.0</TargetFrameworks>
		<Authors>珠海市酷迪技术有限公司</Authors>
		<Version>9.2.0</Version>
		<Authors>珠海市技术有限公司</Authors>
		<Product>Coder.Token.Service</Product>
		<PackageTags>Coder.Token.Service,token</PackageTags>
	</PropertyGroup>
	<ItemGroup Condition=" '$(TargetFramework)' == 'net8.0'">
		<PackageReference Include="Coder.Utility" Version="1.2.2" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />

		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
	</ItemGroup>
	
	<ItemGroup Condition=" '$(TargetFramework)' == 'net9.0'">
		<PackageReference Include="Coder.Utility" Version="1.2.2" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Coder.Token.Abstractions\Coder.Token.Abstractions.csproj" />
	</ItemGroup>
</Project>