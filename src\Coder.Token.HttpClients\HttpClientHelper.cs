﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using Coder.Token.Clients;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Coder.Token.HttpClients;

public static class HttpClientHelper
{
    /// <summary>
    ///     header 头
    /// </summary>
    private const string SignatureHeaderName = "signature";

    public static async Task<TokenResult<TResult>> Execute<TResult>(this HttpClient httpClient,
        Func<Task<HttpResponseMessage>> action,
        string encryptSignature = null,
        string jwtHeader = null, 
        ILogger logger = null,
        CancellationToken cancellationToken = default)
        where TResult : new()
    {
        try
        {
            // 避免重复清除头部，只在必要时设置
            if (!string.IsNullOrEmpty(jwtHeader) && 
                !httpClient.DefaultRequestHeaders.Contains("Authorization"))
            {
                httpClient.DefaultRequestHeaders.Add("Authorization", jwtHeader);
            }
            
            if (!string.IsNullOrEmpty(encryptSignature) && 
                !httpClient.DefaultRequestHeaders.Contains(SignatureHeaderName))
            {
                httpClient.DefaultRequestHeaders.Add(SignatureHeaderName, encryptSignature);
            }

            var result = await action().ConfigureAwait(false);
            return await ToObject<TokenResult<TResult>>(result, logger, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "访问出错");
            return new TokenResult<TResult>
            {
                Message = ex.Message,
                Code = -1
            };
        }
    }

    public static async Task<T> ToObject<T>(this HttpResponseMessage response, 
        ILogger? logger, 
        CancellationToken cancellationToken = default)
        where T : TokenResult, new()
    {
        if (response.IsSuccessStatusCode) 
            return await ToResponseObject<T>(response, logger, cancellationToken).ConfigureAwait(false);

        // 避免阻塞调用
        var content = await response.Content.ReadAsStringAsync(cancellationToken).ConfigureAwait(false);
        logger?.LogWarning("HTTP请求失败: {StatusCode}, 内容: {Content}", response.StatusCode, content);

        var t = new T
        {
            Code = (int)response.StatusCode,
            Message = content
        };
        return t;
    }

    private static async Task<T> ToResponseObject<T>(HttpResponseMessage response, 
        ILogger? logger, 
        CancellationToken cancellationToken = default)
    {
        var body = await response.Content.ReadAsStringAsync(cancellationToken).ConfigureAwait(false);
        
        if (logger?.IsEnabled(LogLevel.Debug) ?? false) 
            logger.LogDebug("返回数据: {ResponseBody}", body);
            
        var result = JsonConvert.DeserializeObject<T>(body);
        return result;
    }
}