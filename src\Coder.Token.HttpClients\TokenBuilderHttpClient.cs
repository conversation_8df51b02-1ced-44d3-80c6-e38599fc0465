﻿using System;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Coder.Authentication;
using Coder.Token.Clients;
using Coder.Token.ViewModels;
using Coder.Utility;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Coder.Token.HttpClients;

public class TokenBuilderHttpClient : ITokenBuilderClient
{
    public const string ServiceKey = "CoderTokenClient";
    private readonly HttpClient _httpClient;
    private readonly string _prefix;
    private readonly ILogger<TokenBuilderHttpClient> _logger;

    /// <summary>
    /// </summary>
    /// <param name="httpClient"></param>
    /// <param name="secretKey">key</param>
    /// <param name="client">客户端</param>
    public TokenBuilderHttpClient(HttpClient httpClient, string secretKey, string client, string? prefix,
        ILogger<TokenBuilderHttpClient>? logger)
    {
        _httpClient = httpClient;
        _prefix = prefix;
        SecretKey = secretKey ?? throw new ArgumentNullException(nameof(secretKey));
        Client = client ?? throw new ArgumentNullException(nameof(client));
        _logger = logger;
    }

    /// <inheritdoc />
    public string Client { get; set; }

    /// <inheritdoc />
    public string SecretKey { get; set; }

    /// <inheritdoc />
    public async Task<TokenResult<CreateTokenResult>> CreateTokenAsync(CreateTokenSubmit submit)
    {
        if (submit == null) throw new ArgumentNullException(nameof(submit));
        submit.Client = this.Client;
        var signature = BuildSignature(submit, out var content);
        _httpClient.DefaultRequestHeaders.Add("signature", signature);
        var resp = await _httpClient.PostAsync($"{_prefix ?? ""}TokenBuilder/create-token", content);

        await CheckResponseAndThrowIfError(resp);

        return JsonConvert.DeserializeObject<TokenResult<CreateTokenResult>>(await resp.Content.ReadAsStringAsync());
    }

    /// <inheritdoc />
    public async Task<TokenResult<CreateTokenTicketResult>> CreateTicketAsync(CreateTokenSubmit submit)
    {
        if (submit == null) throw new ArgumentNullException(nameof(submit));
        submit.Client = this.Client;
        var signature = BuildSignature(submit, out var content);
        _httpClient.DefaultRequestHeaders.Add("signature", signature);
        var resp = await _httpClient.PostAsync($"{_prefix ?? ""}TokenBuilder/create-ticket", content);

        await CheckResponseAndThrowIfError(resp);

        return JsonConvert.DeserializeObject<TokenResult<CreateTokenTicketResult>>(
            await resp.Content.ReadAsStringAsync());
    }

    /// <inheritdoc />
    public async Task<TokenResult<CreateTokenResult>> CreateTokenByTicketAsync(string submitTicket)
    {
        if (submitTicket == null) throw new ArgumentNullException(nameof(submitTicket));

        var url = $"{_prefix ?? ""}TokenBuilder/create-token-by-ticket?ticket={submitTicket}";
        var resp = await _httpClient.GetAsync(url);

        await CheckResponseAndThrowIfError(resp);

        return JsonConvert.DeserializeObject<TokenResult<CreateTokenResult>>(await resp.Content.ReadAsStringAsync());
    }

    public async Task<TokenResult<ClientInfoResult>> GetClientInfo(ClientInfoSubmit submit)
    {
        if (submit == null) throw new ArgumentNullException(nameof(submit));

        var signature = BuildSignature(submit, out var content);
        _httpClient.DefaultRequestHeaders.Add("signature", signature);
        var resp = await _httpClient.PostAsync($"{_prefix ?? ""}TokenBuilder/get-client-info", content);

        await CheckResponseAndThrowIfError(resp);

        return JsonConvert.DeserializeObject<TokenResult<ClientInfoResult>>(await resp.Content.ReadAsStringAsync());
    }

    public async Task<TokenResult> ExpireTokenAsync(RemoveUserTokenSubmit submit)
    {
        if (submit == null) throw new ArgumentNullException(nameof(submit));
        var signature = BuildSignature(submit, out var content);
        _httpClient.DefaultRequestHeaders.Add("signature", signature);

        var resp = await _httpClient.PostAsync($"{_prefix ?? ""}TokenBuilder/remove-token", content);

        await CheckResponseAndThrowIfError(resp);

        return JsonConvert.DeserializeObject<TokenResult>(await resp.Content.ReadAsStringAsync());
    }

    private async Task CheckResponseAndThrowIfError(HttpResponseMessage response)
    {
        if (response.StatusCode == HttpStatusCode.BadRequest || response.StatusCode == HttpStatusCode.Unauthorized)
        {
            var errorMessage = "HTTP request failed";

            // 尝试从 WWW-Authenticate 头部获取错误信息
            if (response.Headers.WwwAuthenticate != null && response.Headers.WwwAuthenticate.Count > 0)
            {
                var wwwAuthenticateValue = string.Join(", ", response.Headers.WwwAuthenticate);
                errorMessage = wwwAuthenticateValue;
            }
            else
            {
                // 如果没有 WWW-Authenticate 头部，则读取响应内容作为错误信息
                var responseContent = await response.Content.ReadAsStringAsync();
                if (!string.IsNullOrEmpty(responseContent))
                {
                    errorMessage = responseContent;
                }
            }

            throw new AuthenticationException($"HTTP {(int)response.StatusCode} {response.StatusCode}: {errorMessage}");
        }

        // 对于其他错误状态码，使用原有的 EnsureSuccessStatusCode 处理
        response.EnsureSuccessStatusCode();
    }

    private string BuildSignature<T>(T submit, out HttpContent content)
    {
        var json = JsonConvert.SerializeObject(submit);
        content = new StringContent(json, Encoding.UTF8, "application/json");
        json += SecretKey;
        if (_logger != null && _logger.IsEnabled(LogLevel.Debug)) _logger?.LogDebug("发起签名内容:" + json);
        using var md5 = MD5.Create();
        var encrypt = md5.ComputeHash(Encoding.UTF8.GetBytes(json));


        return encrypt.ToHexString();
    }
}