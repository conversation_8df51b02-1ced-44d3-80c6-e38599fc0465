﻿namespace Coder.Token.HttpClients;

/// <summary>
/// </summary>
public class TokenClientOptions
{
    private string _host;

    /// <summary>
    ///     服务器
    /// </summary>
    public string Host
    {
        get => _host;
        set
        {
            if (value != null && !value.EndsWith("/"))
            {
                value += "/";
            }
            _host = value;
        }
    }

    /// <summary>
    ///     客户端
    /// </summary>
    public string Client { get; set; }

    /// <summary>
    ///     验证密钥
    /// </summary>
    public string SecretKey { get; set; }
}