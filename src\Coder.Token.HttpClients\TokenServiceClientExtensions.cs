﻿using Coder.Token.Clients;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.VisualBasic.FileIO;
using System;
using System.Net.Http;

namespace Coder.Token.HttpClients;

public static class TokenServiceClientExtensions
{
    /// <summary>
    /// </summary>
    /// <param name="service"></param>
    /// <param name="optionFunc"></param>
    /// <returns></returns>
    public static IServiceCollection AddCoderAuthTokenClient(this IServiceCollection service,
        Action<TokenClientOptions> optionFunc)
    {
        var opt = new TokenClientOptions();
        optionFunc(opt);
        service.AddSingleton(opt);
      

        service.AddHttpClient(TokenBuilderHttpClient.ServiceKey, client =>
        {
            var uri = new Uri(opt.Host);
            string hostPart = uri.GetLeftPart(UriPartial.Authority);
            string pathPart = uri.PathAndQuery;

            client.BaseAddress = new Uri(hostPart);
        });
        return service.AddTransient<ITokenBuilderClient>(sp =>
             {
                 var option = sp.GetRequiredService<TokenClientOptions>();
                 var httpFactory = sp.GetRequiredService<IHttpClientFactory>();
                 var logger = sp.GetRequiredService<ILogger<TokenBuilderHttpClient>>();
                 var client = httpFactory.CreateClient(TokenBuilderHttpClient.ServiceKey);

                 var uri = new Uri(option.Host);
                 string hostPart = uri.GetLeftPart(UriPartial.Authority);
                 string pathPart = uri.PathAndQuery;

                 client.BaseAddress = new Uri(hostPart);
                 client.Timeout = TimeSpan.FromMinutes(3);

                 return new TokenBuilderHttpClient(client, option.SecretKey, option.Client, pathPart, logger);


             });
    }
}