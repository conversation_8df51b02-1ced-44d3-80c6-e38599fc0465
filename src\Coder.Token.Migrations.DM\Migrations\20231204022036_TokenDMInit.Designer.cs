﻿// <auto-generated />
using System;
using Coder.Token;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.Token.Migrations.DM.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20231204022036_TokenDMInit")]
    partial class TokenDMInit
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn)
                .HasAnnotation("ProductVersion", "6.0.25")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            modelBuilder.Entity("Coder.Token.BlackListToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("MoveOutTime")
                        .HasPrecision(0)
                        .HasColumnType("TIMESTAMP");

                    b.Property<string>("TokenSignature")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("UserName")
                        .HasMaxLength(60)
                        .HasColumnType("NVARCHAR2(60)");

                    b.HasKey("Id");

                    b.ToTable("token_blackListToken", (string)null);
                });

            modelBuilder.Entity("Coder.Token.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Audiences")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("SecretKey")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("token_client", (string)null);
                });

            modelBuilder.Entity("Coder.Token.TokenTicket", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Audience")
                        .HasMaxLength(32)
                        .HasColumnType("NVARCHAR2(32)");

                    b.Property<string>("Claims")
                        .HasColumnType("text");

                    b.Property<int?>("ExpireMinutes")
                        .HasColumnType("INT");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasPrecision(0)
                        .HasColumnType("DATETIME WITH TIME ZONE");

                    b.Property<int>("Status")
                        .HasColumnType("INT");

                    b.Property<string>("Ticket")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("Id");

                    b.ToTable("token_token_ticket", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
