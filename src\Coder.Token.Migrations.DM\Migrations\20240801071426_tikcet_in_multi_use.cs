﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.DM.Migrations
{
    public partial class tikcet_in_multi_use : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "INT",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "TokenSignature",
                table: "token_blackListToken",
                type: "NVARCHAR2(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(100)",
                oldMaxLength: 100,
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Remind",
                table: "token_token_ticket");

            migrationBuilder.AlterColumn<string>(
                name: "TokenSignature",
                table: "token_blackListToken",
                type: "NVARCHAR2(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(500)",
                oldMaxLength: 500,
                oldNullable: true);
        }
    }
}
