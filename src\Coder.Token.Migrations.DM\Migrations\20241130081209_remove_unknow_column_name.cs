﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.DM.Migrations
{
    /// <inheritdoc />
    public partial class remove_unknow_column_name : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "text",
                table: "token_token_ticket",
                newName: "Token");

            migrationBuilder.AlterColumn<string>(
                name: "Token",
                table: "token_token_ticket",
                type: "NVARCHAR2(32767)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(8188)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Token",
                table: "token_token_ticket",
                newName: "text");

            migrationBuilder.AlterColumn<string>(
                name: "text",
                table: "token_token_ticket",
                type: "NVARCHAR2(8188)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(32767)",
                oldNullable: true);
        }
    }
}
