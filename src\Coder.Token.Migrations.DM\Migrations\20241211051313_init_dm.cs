﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.DM.Migrations
{
    /// <inheritdoc />
    public partial class init_dm : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "token_blackListToken",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INT", nullable: false)
                        .Annotation("Dm:Identity", "1, 1"),
                    TokenSignature = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    MoveOutTime = table.Column<DateTime>(type: "TIMESTAMP", precision: 0, nullable: false),
                    UserName = table.Column<string>(type: "NVARCHAR2(60)", maxLength: 60, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_blackListToken", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "token_client",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INT", nullable: false)
                        .Annotation("Dm:Identity", "1, 1"),
                    Description = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    Name = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    SecretKey = table.Column<string>(type: "NVARCHAR2(50)", maxLength: 50, nullable: true),
                    Services = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_client", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "token_token_ticket",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INT", nullable: false)
                        .Annotation("Dm:Identity", "1, 1"),
                    Ticket = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    ExpireTime = table.Column<DateTimeOffset>(type: "DATETIME WITH TIME ZONE", precision: 0, nullable: false),
                    Status = table.Column<int>(type: "INT", nullable: false),
                    Claims = table.Column<string>(type: "text", nullable: true),
                    Remind = table.Column<int>(type: "INT", nullable: false),
                    Token = table.Column<string>(type: "NVARCHAR2(32767)", nullable: true),
                    Audience = table.Column<string>(type: "NVARCHAR2(32)", maxLength: 32, nullable: true),
                    ExpireMinutes = table.Column<int>(type: "INT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_token_ticket", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_token_client_Name",
                table: "token_client",
                column: "Name",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "token_blackListToken");

            migrationBuilder.DropTable(
                name: "token_client");

            migrationBuilder.DropTable(
                name: "token_token_ticket");
        }
    }
}
