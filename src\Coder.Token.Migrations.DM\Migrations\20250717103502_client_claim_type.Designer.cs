﻿// <auto-generated />
using System;
using Coder.Token;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.Token.Migrations.DM.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250717103502_client_claim_type")]
    partial class client_claim_type
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn)
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true)
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            modelBuilder.Entity("Coder.Token.BlackListToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("MoveOutTime")
                        .HasPrecision(0)
                        .HasColumnType("TIMESTAMP")
                        .HasComment("黑名单移除事件");

                    b.Property<string>("TokenSignature")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("UserName")
                        .HasMaxLength(60)
                        .HasColumnType("NVARCHAR2(60)")
                        .HasComment("token所属的用户");

                    b.HasKey("Id");

                    b.ToTable("token_blackListToken", null, t =>
                        {
                            t.HasComment("toke黑名单");
                        });
                });

            modelBuilder.Entity("Coder.Token.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("ClaimBuildType")
                        .HasColumnType("INT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)")
                        .HasComment("备注");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)")
                        .HasComment("客户端名称。");

                    b.Property<string>("SecretKey")
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)")
                        .HasComment("加密的key");

                    b.Property<string>("Services")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasComment("客户端能够访问的服务名称");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("token_client", null, t =>
                        {
                            t.HasComment("微服务列表。");
                        });
                });

            modelBuilder.Entity("Coder.Token.TokenTicket", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INT")
                        .HasAnnotation("Dm:ValueGenerationStrategy", DmValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Audience")
                        .HasMaxLength(32)
                        .HasColumnType("NVARCHAR2(32)")
                        .HasComment("授权的服务");

                    b.Property<string>("Claims")
                        .HasColumnType("text")
                        .HasComment("声明值");

                    b.Property<int?>("ExpireMinutes")
                        .HasColumnType("INT")
                        .HasComment("授权的超时时间");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasPrecision(0)
                        .HasColumnType("DATETIME WITH TIME ZONE");

                    b.Property<int>("Remind")
                        .HasColumnType("INT")
                        .HasComment("票据剩余的使用次数。");

                    b.Property<int>("Status")
                        .HasColumnType("INT")
                        .HasComment("票据的状态");

                    b.Property<string>("Ticket")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("Token")
                        .HasColumnType("NVARCHAR2(32767)")
                        .HasComment("生成的token信息");

                    b.HasKey("Id");

                    b.ToTable("token_token_ticket", null, t =>
                        {
                            t.HasComment("获取token的票据");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
