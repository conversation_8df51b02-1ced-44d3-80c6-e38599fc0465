﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.DM.Migrations
{
    /// <inheritdoc />
    public partial class client_claim_type : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterTable(
                name: "token_token_ticket",
                comment: "获取token的票据");

            migrationBuilder.AlterTable(
                name: "token_client",
                comment: "微服务列表。");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                comment: "toke黑名单");

            migrationBuilder.AlterColumn<string>(
                name: "Token",
                table: "token_token_ticket",
                type: "NVARCHAR2(32767)",
                nullable: true,
                comment: "生成的token信息",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(32767)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                table: "token_token_ticket",
                type: "INT",
                nullable: false,
                comment: "票据的状态",
                oldClrType: typeof(int),
                oldType: "INT");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "INT",
                nullable: false,
                comment: "票据剩余的使用次数。",
                oldClrType: typeof(int),
                oldType: "INT");

            migrationBuilder.AlterColumn<int>(
                name: "ExpireMinutes",
                table: "token_token_ticket",
                type: "INT",
                nullable: true,
                comment: "授权的超时时间",
                oldClrType: typeof(int),
                oldType: "INT",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                comment: "声明值",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Audience",
                table: "token_token_ticket",
                type: "NVARCHAR2(32)",
                maxLength: 32,
                nullable: true,
                comment: "授权的服务",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(32)",
                oldMaxLength: 32,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "NVARCHAR2(256)",
                maxLength: 256,
                nullable: true,
                comment: "客户端能够访问的服务名称",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(256)",
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "NVARCHAR2(50)",
                maxLength: 50,
                nullable: true,
                comment: "加密的key",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "NVARCHAR2(100)",
                maxLength: 100,
                nullable: true,
                comment: "客户端名称。",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "NVARCHAR2(500)",
                maxLength: 500,
                nullable: true,
                comment: "备注",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ClaimBuildType",
                table: "token_client",
                type: "INT",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "NVARCHAR2(60)",
                maxLength: 60,
                nullable: true,
                comment: "token所属的用户",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(60)",
                oldMaxLength: 60,
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "TIMESTAMP",
                precision: 0,
                nullable: false,
                comment: "黑名单移除事件",
                oldClrType: typeof(DateTime),
                oldType: "TIMESTAMP",
                oldPrecision: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ClaimBuildType",
                table: "token_client");

            migrationBuilder.AlterTable(
                name: "token_token_ticket",
                oldComment: "获取token的票据");

            migrationBuilder.AlterTable(
                name: "token_client",
                oldComment: "微服务列表。");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                oldComment: "toke黑名单");

            migrationBuilder.AlterColumn<string>(
                name: "Token",
                table: "token_token_ticket",
                type: "NVARCHAR2(32767)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(32767)",
                oldNullable: true,
                oldComment: "生成的token信息");

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                table: "token_token_ticket",
                type: "INT",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "INT",
                oldComment: "票据的状态");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "INT",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "INT",
                oldComment: "票据剩余的使用次数。");

            migrationBuilder.AlterColumn<int>(
                name: "ExpireMinutes",
                table: "token_token_ticket",
                type: "INT",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INT",
                oldNullable: true,
                oldComment: "授权的超时时间");

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true,
                oldComment: "声明值");

            migrationBuilder.AlterColumn<string>(
                name: "Audience",
                table: "token_token_ticket",
                type: "NVARCHAR2(32)",
                maxLength: 32,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(32)",
                oldMaxLength: 32,
                oldNullable: true,
                oldComment: "授权的服务");

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "NVARCHAR2(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(256)",
                oldMaxLength: 256,
                oldNullable: true,
                oldComment: "客户端能够访问的服务名称");

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "NVARCHAR2(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(50)",
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "加密的key");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "NVARCHAR2(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(100)",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "客户端名称。");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "NVARCHAR2(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(500)",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "备注");

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "NVARCHAR2(60)",
                maxLength: 60,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(60)",
                oldMaxLength: 60,
                oldNullable: true,
                oldComment: "token所属的用户");

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "TIMESTAMP",
                precision: 0,
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "TIMESTAMP",
                oldPrecision: 0,
                oldComment: "黑名单移除事件");
        }
    }
}
