﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Coder.Token.Migrations.Mssql.Migrations
{
    /// <summary>
    /// 
    /// </summary>
    public partial class init_token : Migration
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="migrationBuilder"></param>
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "token_blackListToken",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TokenSignature = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MoveOutTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UserName = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_blackListToken", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "token_client",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    SecretKey = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Audiences = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_client", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "token_token_ticket",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Ticket = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExpireTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Claims = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Audience = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    ExpireMinutes = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_token_ticket", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_token_client_Name",
                table: "token_client",
                column: "Name",
                unique: true,
                filter: "[Name] IS NOT NULL");
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="migrationBuilder"></param>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "token_blackListToken");

            migrationBuilder.DropTable(
                name: "token_client");

            migrationBuilder.DropTable(
                name: "token_token_ticket");
        }
    }
}
