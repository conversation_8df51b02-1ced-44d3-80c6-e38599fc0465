﻿// <auto-generated />
using System;
using Coder.Token;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.Token.Migrations.Mssql.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250831074923_len_change")]
    partial class len_change
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true)
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Coder.Token.BlackListToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("主键标识");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("MoveOutTime")
                        .HasPrecision(0)
                        .HasColumnType("datetime2(0)")
                        .HasComment("黑名单移除时间，到达此时间后 Token 可以从黑名单中移除");

                    b.Property<string>("TokenSignature")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("Token 签名，用于唯一标识被加入黑名单的 Token");

                    b.Property<string>("UserName")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)")
                        .HasComment("Token 所属的用户名");

                    b.HasKey("Id");

                    b.HasIndex("MoveOutTime")
                        .HasDatabaseName("IX_token_blackListToken_MoveOutTime");

                    b.HasIndex("TokenSignature")
                        .IsUnique()
                        .HasDatabaseName("IX_token_blackListToken_TokenSignature");

                    b.HasIndex("UserName")
                        .HasDatabaseName("IX_token_blackListToken_UserName");

                    b.ToTable("token_blackListToken", null, t =>
                        {
                            t.HasComment("Token 黑名单表，存储被加入黑名单的 Token 信息");
                        });
                });

            modelBuilder.Entity("Coder.Token.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("客户端唯一标识符");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClaimBuildType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasComment("JWT声明创建规则类型");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("客户端描述信息");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("客户端名称，用于标识不同的客户端应用");

                    b.Property<string>("SecretKey")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("客户端加密密钥，用于身份验证");

                    b.Property<string>("Services")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasComment("客户端可访问的服务名称列表，以JSON格式存储");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_token_client_Name");

                    b.ToTable("token_client", null, t =>
                        {
                            t.HasComment("微服务客户端列表");
                        });
                });

            modelBuilder.Entity("Coder.Token.TokenTicket", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasComment("票据唯一标识符");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Audience")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasComment("授权的服务");

                    b.Property<string>("Claims")
                        .HasColumnType("text")
                        .HasComment("声明值，以JSON格式存储");

                    b.Property<int?>("ExpireMinutes")
                        .HasColumnType("int")
                        .HasComment("授权的超时时间");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasPrecision(0)
                        .HasColumnType("datetimeoffset(0)")
                        .HasComment("票据过期时间");

                    b.Property<int>("Remind")
                        .HasColumnType("int")
                        .HasComment("票据剩余的使用次数");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("票据的状态");

                    b.Property<string>("Ticket")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("票据标识字符串");

                    b.Property<string>("Token")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("生成的token信息");

                    b.HasKey("Id");

                    b.ToTable("token_token_ticket", null, t =>
                        {
                            t.HasComment("获取token的票据");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
