﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.Mssql.Migrations
{
    /// <inheritdoc />
    public partial class len_change : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_token_client_Name",
                table: "token_client");

            migrationBuilder.AlterTable(
                name: "token_client",
                comment: "微服务客户端列表",
                oldComment: "微服务列表。");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                comment: "Token 黑名单表，存储被加入黑名单的 Token 信息",
                oldComment: "toke黑名单");

            migrationBuilder.AlterColumn<string>(
                name: "Ticket",
                table: "token_token_ticket",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "票据标识字符串",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                comment: "票据剩余的使用次数",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "票据剩余的使用次数。");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "ExpireTime",
                table: "token_token_ticket",
                type: "datetimeoffset(0)",
                precision: 0,
                nullable: false,
                comment: "票据过期时间",
                oldClrType: typeof(DateTimeOffset),
                oldType: "datetimeoffset(0)",
                oldPrecision: 0);

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                comment: "声明值，以JSON格式存储",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true,
                oldComment: "声明值");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                comment: "票据唯一标识符",
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true,
                comment: "客户端可访问的服务名称列表，以JSON格式存储",
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256,
                oldNullable: true,
                oldComment: "客户端能够访问的服务名称");

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                comment: "客户端加密密钥，用于身份验证",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "加密的key");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                comment: "客户端名称，用于标识不同的客户端应用",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "客户端名称。");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "客户端描述信息",
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "备注");

            migrationBuilder.AlterColumn<int>(
                name: "ClaimBuildType",
                table: "token_client",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "JWT声明创建规则类型",
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_client",
                type: "int",
                nullable: false,
                comment: "客户端唯一标识符",
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true,
                comment: "Token 所属的用户名",
                oldClrType: typeof(string),
                oldType: "nvarchar(60)",
                oldMaxLength: 60,
                oldNullable: true,
                oldComment: "token所属的用户");

            migrationBuilder.AlterColumn<string>(
                name: "TokenSignature",
                table: "token_blackListToken",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                comment: "Token 签名，用于唯一标识被加入黑名单的 Token",
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "datetime2(0)",
                precision: 0,
                nullable: false,
                comment: "黑名单移除时间，到达此时间后 Token 可以从黑名单中移除",
                oldClrType: typeof(DateTime),
                oldType: "datetime2(0)",
                oldPrecision: 0,
                oldComment: "黑名单移除事件");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_blackListToken",
                type: "int",
                nullable: false,
                comment: "主键标识",
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.CreateIndex(
                name: "IX_token_client_Name",
                table: "token_client",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_token_blackListToken_MoveOutTime",
                table: "token_blackListToken",
                column: "MoveOutTime");

            migrationBuilder.CreateIndex(
                name: "IX_token_blackListToken_TokenSignature",
                table: "token_blackListToken",
                column: "TokenSignature",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_token_blackListToken_UserName",
                table: "token_blackListToken",
                column: "UserName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_token_client_Name",
                table: "token_client");

            migrationBuilder.DropIndex(
                name: "IX_token_blackListToken_MoveOutTime",
                table: "token_blackListToken");

            migrationBuilder.DropIndex(
                name: "IX_token_blackListToken_TokenSignature",
                table: "token_blackListToken");

            migrationBuilder.DropIndex(
                name: "IX_token_blackListToken_UserName",
                table: "token_blackListToken");

            migrationBuilder.AlterTable(
                name: "token_client",
                comment: "微服务列表。",
                oldComment: "微服务客户端列表");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                comment: "toke黑名单",
                oldComment: "Token 黑名单表，存储被加入黑名单的 Token 信息");

            migrationBuilder.AlterColumn<string>(
                name: "Ticket",
                table: "token_token_ticket",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "票据标识字符串");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                comment: "票据剩余的使用次数。",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "票据剩余的使用次数");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "ExpireTime",
                table: "token_token_ticket",
                type: "datetimeoffset(0)",
                precision: 0,
                nullable: false,
                oldClrType: typeof(DateTimeOffset),
                oldType: "datetimeoffset(0)",
                oldPrecision: 0,
                oldComment: "票据过期时间");

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                comment: "声明值",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true,
                oldComment: "声明值，以JSON格式存储");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "票据唯一标识符")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                comment: "客户端能够访问的服务名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true,
                oldComment: "客户端可访问的服务名称列表，以JSON格式存储");

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                comment: "加密的key",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldComment: "客户端加密密钥，用于身份验证");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                comment: "客户端名称。",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldComment: "客户端名称，用于标识不同的客户端应用");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "备注",
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "客户端描述信息");

            migrationBuilder.AlterColumn<int>(
                name: "ClaimBuildType",
                table: "token_client",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0,
                oldComment: "JWT声明创建规则类型");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_client",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "客户端唯一标识符")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true,
                comment: "token所属的用户",
                oldClrType: typeof(string),
                oldType: "nvarchar(60)",
                oldMaxLength: 60,
                oldNullable: true,
                oldComment: "Token 所属的用户名");

            migrationBuilder.AlterColumn<string>(
                name: "TokenSignature",
                table: "token_blackListToken",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldComment: "Token 签名，用于唯一标识被加入黑名单的 Token");

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "datetime2(0)",
                precision: 0,
                nullable: false,
                comment: "黑名单移除事件",
                oldClrType: typeof(DateTime),
                oldType: "datetime2(0)",
                oldPrecision: 0,
                oldComment: "黑名单移除时间，到达此时间后 Token 可以从黑名单中移除");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_blackListToken",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "主键标识")
                .Annotation("SqlServer:Identity", "1, 1")
                .OldAnnotation("SqlServer:Identity", "1, 1");

            migrationBuilder.CreateIndex(
                name: "IX_token_client_Name",
                table: "token_client",
                column: "Name",
                unique: true,
                filter: "[Name] IS NOT NULL");
        }
    }
}
