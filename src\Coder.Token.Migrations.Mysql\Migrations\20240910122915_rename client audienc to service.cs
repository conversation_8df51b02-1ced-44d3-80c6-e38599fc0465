﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.Mysql
{
    public partial class renameclientaudienctoservice : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Audiences",
                table: "token_client",
                newName: "Services");

            migrationBuilder.AddColumn<string>(
                name: "text",
                table: "token_token_ticket",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "text",
                table: "token_token_ticket");

            migrationBuilder.RenameColumn(
                name: "Services",
                table: "token_client",
                newName: "Audiences");
        }
    }
}
