﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.Mysql
{
    /// <inheritdoc />
    public partial class len_change : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterTable(
                name: "token_client",
                comment: "微服务客户端列表",
                oldComment: "微服务列表。")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                comment: "Token 黑名单表，存储被加入黑名单的 Token 信息",
                oldComment: "toke黑名单")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Ticket",
                table: "token_token_ticket",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "票据标识字符串",
                oldClrType: typeof(string),
                oldType: "varchar(200)",
                oldMaxLength: 200,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                comment: "票据剩余的使用次数",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "票据剩余的使用次数。");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "ExpireTime",
                table: "token_token_ticket",
                type: "datetime(0)",
                precision: 0,
                nullable: false,
                comment: "票据过期时间",
                oldClrType: typeof(DateTimeOffset),
                oldType: "datetime(0)",
                oldPrecision: 0);

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                comment: "声明值，以JSON格式存储",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true,
                oldComment: "声明值")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                comment: "票据唯一标识符",
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "varchar(1024)",
                maxLength: 1024,
                nullable: true,
                comment: "客户端可访问的服务名称列表，以JSON格式存储",
                oldClrType: typeof(string),
                oldType: "varchar(256)",
                oldMaxLength: 256,
                oldNullable: true,
                oldComment: "客户端能够访问的服务名称")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "token_client",
                keyColumn: "SecretKey",
                keyValue: null,
                column: "SecretKey",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "varchar(50)",
                maxLength: 50,
                nullable: false,
                comment: "客户端加密密钥，用于身份验证",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "加密的key")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "token_client",
                keyColumn: "Name",
                keyValue: null,
                column: "Name",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "varchar(100)",
                maxLength: 100,
                nullable: false,
                comment: "客户端名称，用于标识不同的客户端应用",
                oldClrType: typeof(string),
                oldType: "varchar(100)",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "客户端名称。")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "客户端描述信息",
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "备注")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "ClaimBuildType",
                table: "token_client",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "JWT声明创建规则类型",
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_client",
                type: "int",
                nullable: false,
                comment: "客户端唯一标识符",
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "varchar(60)",
                maxLength: 60,
                nullable: true,
                comment: "Token 所属的用户名",
                oldClrType: typeof(string),
                oldType: "varchar(60)",
                oldMaxLength: 60,
                oldNullable: true,
                oldComment: "token所属的用户")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "token_blackListToken",
                keyColumn: "TokenSignature",
                keyValue: null,
                column: "TokenSignature",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "TokenSignature",
                table: "token_blackListToken",
                type: "varchar(500)",
                maxLength: 500,
                nullable: false,
                comment: "Token 签名，用于唯一标识被加入黑名单的 Token",
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "datetime(0)",
                precision: 0,
                nullable: false,
                comment: "黑名单移除时间，到达此时间后 Token 可以从黑名单中移除",
                oldClrType: typeof(DateTime),
                oldType: "datetime(0)",
                oldPrecision: 0,
                oldComment: "黑名单移除事件");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_blackListToken",
                type: "int",
                nullable: false,
                comment: "主键标识",
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.CreateIndex(
                name: "IX_token_blackListToken_MoveOutTime",
                table: "token_blackListToken",
                column: "MoveOutTime");

            migrationBuilder.CreateIndex(
                name: "IX_token_blackListToken_TokenSignature",
                table: "token_blackListToken",
                column: "TokenSignature",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_token_blackListToken_UserName",
                table: "token_blackListToken",
                column: "UserName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_token_blackListToken_MoveOutTime",
                table: "token_blackListToken");

            migrationBuilder.DropIndex(
                name: "IX_token_blackListToken_TokenSignature",
                table: "token_blackListToken");

            migrationBuilder.DropIndex(
                name: "IX_token_blackListToken_UserName",
                table: "token_blackListToken");

            migrationBuilder.AlterTable(
                name: "token_client",
                comment: "微服务列表。",
                oldComment: "微服务客户端列表")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                comment: "toke黑名单",
                oldComment: "Token 黑名单表，存储被加入黑名单的 Token 信息")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Ticket",
                table: "token_token_ticket",
                type: "varchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "票据标识字符串")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                comment: "票据剩余的使用次数。",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "票据剩余的使用次数");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "ExpireTime",
                table: "token_token_ticket",
                type: "datetime(0)",
                precision: 0,
                nullable: false,
                oldClrType: typeof(DateTimeOffset),
                oldType: "datetime(0)",
                oldPrecision: 0,
                oldComment: "票据过期时间");

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                comment: "声明值",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true,
                oldComment: "声明值，以JSON格式存储")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_token_ticket",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "票据唯一标识符")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "varchar(256)",
                maxLength: 256,
                nullable: true,
                comment: "客户端能够访问的服务名称",
                oldClrType: typeof(string),
                oldType: "varchar(1024)",
                oldMaxLength: 1024,
                oldNullable: true,
                oldComment: "客户端可访问的服务名称列表，以JSON格式存储")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                comment: "加密的key",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldMaxLength: 50,
                oldComment: "客户端加密密钥，用于身份验证")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true,
                comment: "客户端名称。",
                oldClrType: typeof(string),
                oldType: "varchar(100)",
                oldMaxLength: 100,
                oldComment: "客户端名称，用于标识不同的客户端应用")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "备注",
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "客户端描述信息")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "ClaimBuildType",
                table: "token_client",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0,
                oldComment: "JWT声明创建规则类型");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_client",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "客户端唯一标识符")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "varchar(60)",
                maxLength: 60,
                nullable: true,
                comment: "token所属的用户",
                oldClrType: typeof(string),
                oldType: "varchar(60)",
                oldMaxLength: 60,
                oldNullable: true,
                oldComment: "Token 所属的用户名")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "TokenSignature",
                table: "token_blackListToken",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldComment: "Token 签名，用于唯一标识被加入黑名单的 Token")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "datetime(0)",
                precision: 0,
                nullable: false,
                comment: "黑名单移除事件",
                oldClrType: typeof(DateTime),
                oldType: "datetime(0)",
                oldPrecision: 0,
                oldComment: "黑名单移除时间，到达此时间后 Token 可以从黑名单中移除");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "token_blackListToken",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "主键标识")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);
        }
    }
}
