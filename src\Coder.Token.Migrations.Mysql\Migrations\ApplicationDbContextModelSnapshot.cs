﻿// <auto-generated />
using System;
using Coder.Token;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.Token.WebApi.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true)
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("Coder.Token.BlackListToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("MoveOutTime")
                        .HasPrecision(0)
                        .HasColumnType("datetime(0)")
                        .HasComment("黑名单移除事件");

                    b.Property<string>("TokenSignature")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("UserName")
                        .HasMaxLength(60)
                        .HasColumnType("varchar(60)")
                        .HasComment("token所属的用户");

                    b.HasKey("Id");

                    b.ToTable("token_blackListToken", null, t =>
                        {
                            t.HasComment("toke黑名单");
                        });
                });

            modelBuilder.Entity("Coder.Token.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClaimBuildType")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasComment("备注");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("客户端名称。");

                    b.Property<string>("SecretKey")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("加密的key");

                    b.Property<string>("Services")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("客户端能够访问的服务名称");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("token_client", null, t =>
                        {
                            t.HasComment("微服务列表。");
                        });
                });

            modelBuilder.Entity("Coder.Token.TokenTicket", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Audience")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("授权的服务");

                    b.Property<string>("Claims")
                        .HasColumnType("text")
                        .HasComment("声明值");

                    b.Property<int?>("ExpireMinutes")
                        .HasColumnType("int")
                        .HasComment("授权的超时时间");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasPrecision(0)
                        .HasColumnType("datetime(0)");

                    b.Property<int>("Remind")
                        .HasColumnType("int")
                        .HasComment("票据剩余的使用次数。");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("票据的状态");

                    b.Property<string>("Ticket")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Token")
                        .HasColumnType("longtext")
                        .HasComment("生成的token信息");

                    b.HasKey("Id");

                    b.ToTable("token_token_ticket", null, t =>
                        {
                            t.HasComment("获取token的票据");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
