﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.Sqlite.Migrations
{
    public partial class TokenDMInit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "token_blackListToken",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    TokenSignature = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MoveOutTime = table.Column<DateTime>(type: "TEXT", precision: 0, nullable: false),
                    UserName = table.Column<string>(type: "TEXT", maxLength: 60, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_blackListToken", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "token_client",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    SecretKey = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Audiences = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_client", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "token_token_ticket",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Ticket = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ExpireTime = table.Column<DateTimeOffset>(type: "TEXT", precision: 0, nullable: false),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    Claims = table.Column<string>(type: "text", nullable: true),
                    Audience = table.Column<string>(type: "TEXT", maxLength: 32, nullable: true),
                    ExpireMinutes = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_token_token_ticket", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_token_client_Name",
                table: "token_client",
                column: "Name",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "token_blackListToken");

            migrationBuilder.DropTable(
                name: "token_client");

            migrationBuilder.DropTable(
                name: "token_token_ticket");
        }
    }
}
