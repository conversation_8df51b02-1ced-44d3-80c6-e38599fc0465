﻿// <auto-generated />
using System;
using Coder.Token;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.Token.Migrations.Sqlite.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20240801071420_tikcet_in_multi_use")]
    partial class tikcet_in_multi_use
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "6.0.32");

            modelBuilder.Entity("Coder.Token.BlackListToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("MoveOutTime")
                        .HasPrecision(0)
                        .HasColumnType("TEXT");

                    b.Property<string>("TokenSignature")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasMaxLength(60)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("token_blackListToken", (string)null);
                });

            modelBuilder.Entity("Coder.Token.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Audiences")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SecretKey")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("token_client", (string)null);
                });

            modelBuilder.Entity("Coder.Token.TokenTicket", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Audience")
                        .HasMaxLength(32)
                        .HasColumnType("TEXT");

                    b.Property<string>("Claims")
                        .HasColumnType("text");

                    b.Property<int?>("ExpireMinutes")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasPrecision(0)
                        .HasColumnType("TEXT");

                    b.Property<int>("Remind")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Ticket")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("token_token_ticket", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
