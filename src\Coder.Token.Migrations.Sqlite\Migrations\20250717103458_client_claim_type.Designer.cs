﻿// <auto-generated />
using System;
using Coder.Token;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.Token.Migrations.Sqlite.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250717103458_client_claim_type")]
    partial class client_claim_type
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true);

            modelBuilder.Entity("Coder.Token.BlackListToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("MoveOutTime")
                        .HasPrecision(0)
                        .HasColumnType("TEXT")
                        .HasComment("黑名单移除事件");

                    b.Property<string>("TokenSignature")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasMaxLength(60)
                        .HasColumnType("TEXT")
                        .HasComment("token所属的用户");

                    b.HasKey("Id");

                    b.ToTable("token_blackListToken", null, t =>
                        {
                            t.HasComment("toke黑名单");
                        });
                });

            modelBuilder.Entity("Coder.Token.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClaimBuildType")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("备注");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("客户端名称。");

                    b.Property<string>("SecretKey")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("加密的key");

                    b.Property<string>("Services")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT")
                        .HasComment("客户端能够访问的服务名称");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("token_client", null, t =>
                        {
                            t.HasComment("微服务列表。");
                        });
                });

            modelBuilder.Entity("Coder.Token.TokenTicket", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Audience")
                        .HasMaxLength(32)
                        .HasColumnType("TEXT")
                        .HasComment("授权的服务");

                    b.Property<string>("Claims")
                        .HasColumnType("text")
                        .HasComment("声明值");

                    b.Property<int?>("ExpireMinutes")
                        .HasColumnType("INTEGER")
                        .HasComment("授权的超时时间");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasPrecision(0)
                        .HasColumnType("TEXT");

                    b.Property<int>("Remind")
                        .HasColumnType("INTEGER")
                        .HasComment("票据剩余的使用次数。");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER")
                        .HasComment("票据的状态");

                    b.Property<string>("Ticket")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Token")
                        .HasColumnType("TEXT")
                        .HasComment("生成的token信息");

                    b.HasKey("Id");

                    b.ToTable("token_token_ticket", null, t =>
                        {
                            t.HasComment("获取token的票据");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
