﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.Token.Migrations.Sqlite.Migrations
{
    /// <inheritdoc />
    public partial class client_claim_type : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterTable(
                name: "token_token_ticket",
                comment: "获取token的票据");

            migrationBuilder.AlterTable(
                name: "token_client",
                comment: "微服务列表。");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                comment: "toke黑名单");

            migrationBuilder.AlterColumn<string>(
                name: "Token",
                table: "token_token_ticket",
                type: "TEXT",
                nullable: true,
                comment: "生成的token信息",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                table: "token_token_ticket",
                type: "INTEGER",
                nullable: false,
                comment: "票据的状态",
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "INTEGER",
                nullable: false,
                comment: "票据剩余的使用次数。",
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<int>(
                name: "ExpireMinutes",
                table: "token_token_ticket",
                type: "INTEGER",
                nullable: true,
                comment: "授权的超时时间",
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                comment: "声明值",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Audience",
                table: "token_token_ticket",
                type: "TEXT",
                maxLength: 32,
                nullable: true,
                comment: "授权的服务",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 32,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "TEXT",
                maxLength: 256,
                nullable: true,
                comment: "客户端能够访问的服务名称",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "TEXT",
                maxLength: 50,
                nullable: true,
                comment: "加密的key",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "客户端名称。",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                comment: "备注",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ClaimBuildType",
                table: "token_client",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "TEXT",
                maxLength: 60,
                nullable: true,
                comment: "token所属的用户",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 60,
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "TEXT",
                precision: 0,
                nullable: false,
                comment: "黑名单移除事件",
                oldClrType: typeof(DateTime),
                oldType: "TEXT",
                oldPrecision: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ClaimBuildType",
                table: "token_client");

            migrationBuilder.AlterTable(
                name: "token_token_ticket",
                oldComment: "获取token的票据");

            migrationBuilder.AlterTable(
                name: "token_client",
                oldComment: "微服务列表。");

            migrationBuilder.AlterTable(
                name: "token_blackListToken",
                oldComment: "toke黑名单");

            migrationBuilder.AlterColumn<string>(
                name: "Token",
                table: "token_token_ticket",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true,
                oldComment: "生成的token信息");

            migrationBuilder.AlterColumn<int>(
                name: "Status",
                table: "token_token_ticket",
                type: "INTEGER",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldComment: "票据的状态");

            migrationBuilder.AlterColumn<int>(
                name: "Remind",
                table: "token_token_ticket",
                type: "INTEGER",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldComment: "票据剩余的使用次数。");

            migrationBuilder.AlterColumn<int>(
                name: "ExpireMinutes",
                table: "token_token_ticket",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true,
                oldComment: "授权的超时时间");

            migrationBuilder.AlterColumn<string>(
                name: "Claims",
                table: "token_token_ticket",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true,
                oldComment: "声明值");

            migrationBuilder.AlterColumn<string>(
                name: "Audience",
                table: "token_token_ticket",
                type: "TEXT",
                maxLength: 32,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 32,
                oldNullable: true,
                oldComment: "授权的服务");

            migrationBuilder.AlterColumn<string>(
                name: "Services",
                table: "token_client",
                type: "TEXT",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 256,
                oldNullable: true,
                oldComment: "客户端能够访问的服务名称");

            migrationBuilder.AlterColumn<string>(
                name: "SecretKey",
                table: "token_client",
                type: "TEXT",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "加密的key");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "token_client",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "客户端名称。");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "token_client",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "备注");

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "token_blackListToken",
                type: "TEXT",
                maxLength: 60,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 60,
                oldNullable: true,
                oldComment: "token所属的用户");

            migrationBuilder.AlterColumn<DateTime>(
                name: "MoveOutTime",
                table: "token_blackListToken",
                type: "TEXT",
                precision: 0,
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "TEXT",
                oldPrecision: 0,
                oldComment: "黑名单移除事件");
        }
    }
}
