﻿using Microsoft.EntityFrameworkCore;

namespace Coder.Token;

/// <summary>
/// </summary>
public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext()
    {
        
    }
    /// <summary>
    /// </summary>
    /// <param name="options"></param>
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }


    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.AddEfModels();
        base.OnModelCreating(builder);
    }
}