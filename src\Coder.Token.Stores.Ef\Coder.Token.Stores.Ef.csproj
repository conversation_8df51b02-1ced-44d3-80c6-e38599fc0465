<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Version>9.1.4</Version>
		<RootNamespace>Coder.Token</RootNamespace>
		<Product>Coder.Token.Service</Product>
		<Authors>珠海市酷迪技术有限公司</Authors>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Innofactor.EfCoreJsonValueConverter" Version="6.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Coder.Token.Core\Coder.Token.Core.csproj" />
	</ItemGroup>
</Project>