﻿using Coder.Token.Mapping;
using Coder.Token.Stores;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.Token;

public static class CoderTokenStoreExtensions
{
    public static TokenServiceOption AddEfStores<TDbContext>(this TokenServiceOption options)
        where TDbContext : DbContext
    {
        options.Services.AddScoped<ITokenBlackListStore, TokenBlackListStore<TDbContext>>();
        options.Services.AddScoped<IClientStore, ClientStore<TDbContext>>()
            .AddScoped<ITokenTicketStore, TokenTicketStore<TDbContext>>()
            ;
        return options;
    }

    public static ModelBuilder AddEfModels(this ModelBuilder builder, string prefix = "token", bool isSqlite = false)
    {
        builder.ApplyConfiguration(new ClientMapping(prefix));
        builder.ApplyConfiguration(new TokenTicketMapping(prefix, isSqlite));
        builder.ApplyConfiguration(new BlackListItemMapping(prefix));
        return builder;
    }
}