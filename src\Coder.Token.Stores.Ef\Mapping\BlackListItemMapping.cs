﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.Token.Mapping;

/// <summary>
/// Token 黑名单实体映射配置
/// </summary>
internal sealed class BlackListItemMapping : IEntityTypeConfiguration<BlackListToken>
{
    private readonly string _prefix;

    /// <summary>
    /// 初始化 BlackListItemMapping 实例
    /// </summary>
    /// <param name="prefix">表名前缀</param>
    /// <exception cref="ArgumentNullException">当 prefix 为 null 时抛出</exception>
    public BlackListItemMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    /// <summary>
    /// 配置 BlackListToken 实体映射
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<BlackListToken> builder)
    {
        ArgumentNullException.ThrowIfNull(builder);

        // 主键配置
        builder.HasKey(entity => entity.Id);
        builder.Property(entity => entity.Id)
               .ValueGeneratedOnAdd()
               .HasComment("主键标识");

        // Token 签名配置
        builder.Property(entity => entity.TokenSignature)
               .IsRequired()
               .HasMaxLength(500)
               .HasComment("Token 签名，用于唯一标识被加入黑名单的 Token");

        // 创建索引以提高查询性能
        builder.HasIndex(entity => entity.TokenSignature)
               .IsUnique()
               .HasDatabaseName($"IX_{_prefix}_blackListToken_TokenSignature");

        // 移出时间配置
        builder.Property(entity => entity.MoveOutTime)
               .IsRequired()
               .HasPrecision(0)
               .HasComment("黑名单移除时间，到达此时间后 Token 可以从黑名单中移除");

        // 创建索引以支持按时间清理过期记录
        builder.HasIndex(entity => entity.MoveOutTime)
               .HasDatabaseName($"IX_{_prefix}_blackListToken_MoveOutTime");

        // 用户名配置
        builder.Property(entity => entity.UserName)
               .HasMaxLength(60)
               .HasComment("Token 所属的用户名");

        // 可选：为用户名创建索引以支持按用户查询
        builder.HasIndex(entity => entity.UserName)
               .HasDatabaseName($"IX_{_prefix}_blackListToken_UserName");

        // 表配置
        builder.ToTable($"{_prefix}_blackListToken", t =>
        {
            t.HasComment("Token 黑名单表，存储被加入黑名单的 Token 信息");
        });


        // 可选：添加检查约束确保 MoveOutTime 大于当前时间（如果数据库支持）
        // builder.HasCheckConstraint("CK_BlackListToken_MoveOutTime", 
        //     "MoveOutTime > GETDATE()");
    }
}