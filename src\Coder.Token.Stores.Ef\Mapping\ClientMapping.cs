﻿using System;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.Token.Mapping;

/// <summary>
/// 客户端实体的数据库映射配置
/// </summary>
internal class ClientMapping : IEntityTypeConfiguration<Client>
{
    private readonly string _prefix;

    /// <summary>
    /// 初始化客户端映射配置
    /// </summary>
    /// <param name="prefix">表名前缀</param>
    /// <exception cref="ArgumentNullException">当前缀为null时抛出</exception>
    public ClientMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    /// <summary>
    /// 配置客户端实体的数据库映射
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<Client> builder)
    {
        // 配置表名和注释
        builder.ToTable($"{_prefix}_client", t => t.HasComment("微服务客户端列表"));

        // 配置主键
        ConfigurePrimaryKey(builder);

        // 配置必需属性
        ConfigureRequiredProperties(builder);

        // 配置可选属性
        ConfigureOptionalProperties(builder);

        // 配置复杂属性
        ConfigureComplexProperties(builder);

        // 配置索引
        ConfigureIndexes(builder);
    }

    /// <summary>
    /// 配置主键
    /// </summary>
    private static void ConfigurePrimaryKey(EntityTypeBuilder<Client> builder)
    {
        builder.HasKey(c => c.Id);
        builder.Property(c => c.Id)
               .ValueGeneratedOnAdd()
               .HasComment("客户端唯一标识符");
    }

    /// <summary>
    /// 配置必需属性
    /// </summary>
    private static void ConfigureRequiredProperties(EntityTypeBuilder<Client> builder)
    {
        // 客户端名称 - 必需且唯一
        builder.Property(c => c.Name)
               .IsRequired()
               .HasMaxLength(100)
               .HasComment("客户端名称，用于标识不同的客户端应用");

        // 密钥 - 必需
        builder.Property(c => c.SecretKey)
               .IsRequired()
               .HasMaxLength(50)
               .HasComment("客户端加密密钥，用于身份验证");
    }

    /// <summary>
    /// 配置可选属性
    /// </summary>
    private static void ConfigureOptionalProperties(EntityTypeBuilder<Client> builder)
    {
        // 描述信息
        builder.Property(c => c.Description)
               .HasMaxLength(500)
               .HasComment("客户端描述信息");

        // 声明构建类型，设置默认值
        builder.Property(c => c.ClaimBuildType)
               .HasDefaultValue(ClaimBuildType.Client)
               .HasComment("JWT声明创建规则类型");
    }

    /// <summary>
    /// 配置复杂属性
    /// </summary>
    private static void ConfigureComplexProperties(EntityTypeBuilder<Client> builder)
    {
        // 服务列表 - JSON存储
        builder.Property(c => c.Services)
               .HasJsonValueConversion()
               .HasMaxLength(1024)
               .HasComment("客户端可访问的服务名称列表，以JSON格式存储");
    }

    /// <summary>
    /// 配置索引
    /// </summary>
    private static void ConfigureIndexes(EntityTypeBuilder<Client> builder)
    {
        // 客户端名称唯一索引
        builder.HasIndex(c => c.Name)
               .IsUnique()
               .HasDatabaseName($"IX_{builder.Metadata.GetTableName()}_Name");
    }
}