﻿using System;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.Token.Mapping;

/// <summary>
///     Token票据实体的数据库映射配置
/// </summary>
internal class TokenTicketMapping : IEntityTypeConfiguration<TokenTicket>
{
    private readonly bool _isSqlite;
    private readonly string _prefix;

    /// <summary>
    ///     初始化Token票据映射配置
    /// </summary>
    /// <param name="prefix">表名前缀</param>
    /// <param name="isSqlite">是否为SQLite数据库</param>
    /// <exception cref="ArgumentNullException">当前缀为null时抛出</exception>
    public TokenTicketMapping(string prefix, bool isSqlite)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
        _isSqlite = isSqlite;
    }

    /// <summary>
    ///     配置Token票据实体的数据库映射
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<TokenTicket> builder)
    {
        // 配置表名和注释
        builder.ToTable($"{_prefix}_token_ticket", t => t.HasComment("获取token的票据"));

        // 配置主键
        ConfigurePrimaryKey(builder);

        // 配置基本属性
        ConfigureBasicProperties(builder);

        // 配置时间属性
        ConfigureTimeProperties(builder);

        // 配置复杂属性
        ConfigureComplexProperties(builder);
    }

    /// <summary>
    ///     配置主键
    /// </summary>
    private static void ConfigurePrimaryKey(EntityTypeBuilder<TokenTicket> builder)
    {
        builder.HasKey(ticket => ticket.Id);
        builder.Property(ticket => ticket.Id)
            .ValueGeneratedOnAdd()
            .HasComment("票据唯一标识符");
    }

    /// <summary>
    ///     配置基本属性
    /// </summary>
    private static void ConfigureBasicProperties(EntityTypeBuilder<TokenTicket> builder)
    {
        // 票据字符串
        builder.Property(ticket => ticket.Ticket)
            .HasMaxLength(200)
            .HasComment("票据标识字符串");

        // 票据状态
        builder.Property(ticket => ticket.Status)
            .HasComment("票据的状态");

        // 目标受众
        builder.Property(ticket => ticket.Audience)
            .HasMaxLength(32)
            .HasComment("授权的服务");

        // 过期时间（分钟）
        builder.Property(ticket => ticket.ExpireMinutes)
            .HasComment("授权的超时时间");

        // 剩余使用次数
        builder.Property(ticket => ticket.Remind)
            .HasComment("票据剩余的使用次数");
    }

    /// <summary>
    ///     配置时间属性
    /// </summary>
    private void ConfigureTimeProperties(EntityTypeBuilder<TokenTicket> builder)
    {
        var expireTime = builder.Property(ticket => ticket.ExpireTime)
            .HasPrecision(0)
            .HasComment("票据过期时间");

        // SQLite特殊处理
        if (_isSqlite) expireTime.HasConversion(typeof(long));
    }

    /// <summary>
    ///     配置复杂属性
    /// </summary>
    private static void ConfigureComplexProperties(EntityTypeBuilder<TokenTicket> builder)
    {
        // 声明值 - JSON存储
        builder.Property(ticket => ticket.Claims)
            .HasColumnType("text")
            .HasJsonValueConversion()
            .HasComment("声明值，以JSON格式存储");

        // Token信息
        builder.Property(ticket => ticket.Token)
            .HasComment("生成的token信息");
    }
}