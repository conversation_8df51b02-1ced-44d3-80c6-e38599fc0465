﻿using Coder.Token.ViewModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Coder.Token.Stores;

internal class ClientStore<T> : IClientStore where T : DbContext
{
    private readonly T _dbContext;

    public ClientStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<IEnumerable<Client>> ListAsync(ClientSearcher search)
    {
        var query = _dbContext.Set<Client>();

        var r = await query.Skip((search.Page - 1) * search.PageSize).Take(search.PageSize).ToListAsync();
        return r;
    }

    public async Task<Client?> GetByIdAsync(int id)
    {
        var query = await _dbContext.Set<Client>().FirstOrDefaultAsync(client => client.Id == id);
        return query;
    }

    public void AddOrUpdate(Client class1)
    {
        _dbContext.Update(class1);
    }

    public Task SaveChangeAsync()
    {
        return _dbContext.SaveChangesAsync();
    }
    [return: MaybeNull]
    public Client Get(string clientName)
    {
        if (clientName == null) throw new ArgumentNullException(nameof(clientName));
        return _dbContext.Set<Client>().FirstOrDefault(_ => _.Name == clientName);
    }
    public Task<Client?> GetAsync(string clientName)
    {
        var t = _dbContext.Set<Client>().FirstOrDefaultAsync(_ => _.Name == clientName);
        return t;
    }
    public bool Exist(string name, int? excludeId)
    {
        if (excludeId == null) return _dbContext.Set<Client>().Any(_ => _.Name == name);

        return _dbContext.Set<Client>().Any(_ => _.Name == name && _.Id != excludeId);
    }

    public void Delete(string name)
    {
        var entity = Get(name);
        if (entity != null)
        {
            _dbContext.Remove(entity);
        }
    }

    public async Task<int> CountAsync(ClientSearcher search)
    {
        var query = _dbContext.Set<Client>();

        return await query.CountAsync();
    }

    public Client? GetById(int submitId)
    {
        return _dbContext.Set<Client>().FirstOrDefault(_ => _.Id == submitId);
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await GetByIdAsync(id);
        if (entity != null)
        {
            _dbContext.Remove(entity);
        }
    }

    public Task<Client?> GetAsync(int id)
    {
        return _dbContext.Set<Client>().FirstOrDefaultAsync(_ => _.Id == id);
    }
}