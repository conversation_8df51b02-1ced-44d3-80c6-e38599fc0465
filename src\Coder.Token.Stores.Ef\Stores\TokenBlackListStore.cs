﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Coder.Token.Stores;

internal class TokenBlackListStore<T> : ITokenBlackListStore where T : DbContext
{
    private readonly T _dbContext;

    public TokenBlackListStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    public void AddOrUpdate(BlackListToken blackListToken)
    {
        _dbContext.Update(blackListToken);
    }

    public Task SaveChangeAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    public IEnumerable<BlackListToken> GetEffectBlackList(int top, DateTime now)
    {
        return _dbContext.Set<BlackListToken>().Where(_ => _.MoveOutTime > now).Take(top)
            .ToList();
    }
}