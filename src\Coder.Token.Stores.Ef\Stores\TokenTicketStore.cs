﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.EntityFrameworkCore;

namespace Coder.Token.Stores;

internal class TokenTicketStore<T> : ITokenTicketStore where T : DbContext
{
    private readonly T _dbContext;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="dbContext"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public TokenTicketStore(T dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public void AddOrUpdate(TokenTicket client)
    {
        ArgumentNullException.ThrowIfNull(client);
        _dbContext.Update(client);
    }


    public Task SaveChangeAsync(CancellationToken cancellationToken = default)
    {
        return _dbContext.SaveChangesAsync(cancellationToken);
    }

    [return: MaybeNull]
    public async Task<TokenTicket?> GetByEffectAsync(string ticket)
    {
        ArgumentException.ThrowIfNullOrEmpty(ticket);

        // 使用参数化查询提高性能，避免DM数据库的DateTimeOffset问题
        var currentTime = DateTimeOffset.Now;

        var ticketInstance = await _dbContext.Set<TokenTicket>()
            .Where(instance => instance.Ticket == ticket &&
                               instance.ExpireTime > currentTime &&
                               instance.Status == TokenTicketStatus.Wait)
            .FirstOrDefaultAsync();


        return ticketInstance;
    }


}