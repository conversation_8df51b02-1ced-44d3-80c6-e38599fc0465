﻿using System;
using Coder.Token.Stores;
using Coder.Token.TokenCache;
using Microsoft.Extensions.Caching.Memory;
using Moq;
using Xunit;

namespace Coder.Token.UnitTest;

public class ClientManagerTest
{
    [Fact]
    public void Get_WithValidKey_ReturnsClient_fromDB()
    {
        var cache = new Mock<IMemoryCache>();
        var cacheMock = new Mock<ClientCache>(new object[] { cache.Object });
        var outClient = new Client
        {
            Name = "validKey",
            Id = 1
        };
        cacheMock.Setup(clientCache => clientCache.GetByName(It.IsAny<string>()))
            .Returns(() => null);

        var store = new Mock<IClientStore>();
        store.Setup(a => a.Get(It.IsAny<string>())).Returns(outClient);
        var clientService = new ClientManager(store.Object, cacheMock.Object);

        // Arrange
        var key = "validKey";


        // Act
        var result = clientService.Get(key);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(outClient, result);
    }

    [Fact]
    public void Get_WithValidKey_ReturnsClient_fromCache()
    {
        var cache = new Mock<IMemoryCache>();
        var cacheMock = new Mock<ClientCache>(new object[] { cache.Object });
        var outClient = new Client
        {
            Name = "validKey",
            Id = 1
        };
        cacheMock.Setup(clientCache => clientCache.GetByName(It.IsAny<string>()))
            .Returns(outClient);

        var store = new Mock<IClientStore>().Object;
        var clientService = new ClientManager(store, cacheMock.Object);

        // Arrange
        var key = "validKey";


        // Act
        var result = clientService.Get(key);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(outClient, result);
    }

    [Fact]
    public void Get_WithNullKey_ThrowsArgumentNullException()
    {
        var cache = new Mock<IMemoryCache>();
        var cacheMock = new Mock<ClientCache>(new object[] { cache.Object });
        var store = new Mock<IClientStore>().Object;
        var _clientService = new ClientManager(store, cacheMock.Object);
        // Arrange
        string key = null;

        // Act + Assert
        Assert.Throws<ArgumentNullException>(() => _clientService.Get(key));
    }

    [Fact]
    public void Get_WithNonExistingKey_ReturnsNull()
    {
        var cache = new Mock<IMemoryCache>();
        var cacheMock = new Mock<ClientCache>(new object[] { cache.Object });
        var store = new Mock<IClientStore>().Object;
        var _clientService = new ClientManager(store, cacheMock.Object);
        // Arrange
        var key = "nonExisting";

        // Act
        var result = _clientService.Get(key);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void ConstrctuorThrowException()
    {
        var cache = new Mock<IMemoryCache>();
        // 将以下代码：
        // var cacheMock = new Mock<ClientCache>(() => new ClientCache(cache.Object));
        // 替换为：
        var cacheMock = new Mock<ClientCache>(new object[] { cache.Object });
     
        var store = new Mock<IClientStore>().Object;
        Assert.Throws(typeof(ArgumentNullException), () => new ClientManager(null, cacheMock.Object));

        Assert.Throws(typeof(ArgumentNullException), () => new ClientManager(store, null));
    }
}