<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
   
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="xunit" Version="2.9.0" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="JunitXml.TestLogger" Version="6.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Coder.Token.Abstractions\Coder.Token.Abstractions.csproj" />
    <ProjectReference Include="..\Coder.Token.HttpClients\Coder.Token.HttpClients.csproj" />
    <ProjectReference Include="..\Coder.Token.WebApi\Coder.Token.WebApi.csproj" />
  </ItemGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties appsettings_1json__JsonSchema="" />
    </VisualStudio>
  </ProjectExtensions>
</Project>