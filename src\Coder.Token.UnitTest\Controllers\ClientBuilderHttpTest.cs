﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Coder.Authentication;
using Coder.Token.Clients;
using Coder.Token.HttpClients;
using Coder.Token.UnitTest.Http.Helper;
using Coder.Token.ViewModels;
using Newtonsoft.Json;
using Xunit;

namespace Coder.Token.UnitTest.Controllers;

public class ClientBuilderHttpTest : IClassFixture<CoderWebAppFactory<TestStartup>>, IDisposable
{
    private const string ClientName = "Member";
    private const string SecretKey = "123467";
    private readonly CoderWebAppFactory<TestStartup> _factory;

    /// <summary>
    /// </summary>
    /// <param name="factory"></param>
    public ClientBuilderHttpTest(CoderWebAppFactory<TestStartup> factory)
    {
        _factory = factory;

    }

    public void Dispose()
    {
    }

    //private async Task AddMockClient()
    //{
    //    var httpClient = _factory.CreateClient();

    //    var submit = new ClientSubmit
    //    {
    //        Name = ClientName,
    //        Services = new List<string>
    //        {
    //            "Token"
    //        },
    //        Description = "测试",
    //        SecretKey = SecretKey,
    //        ClaimBuildType = ClaimBuildType.Both
    //    };

    //    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BuildToken());

    //    var content = JsonConvert.SerializeObject(submit);
    //    var jsonContent = new StringContent(content, Encoding.UTF8, "application/json");

    //    var resp = await httpClient.PostAsync("client/save", jsonContent);
    //}

    //private string BuildToken()
    //{
    //    var httpClient = _factory.CreateClient();
    //    var tokeClient = new TokenBuilderHttpClient(httpClient, "123467", "Member", null, null);
    //    var claims = new List<ClaimSubmit>
    //    {
    //        new(ClaimTypes.Name, "admin"),
    //        new(ClaimTypes.Role, "admin")
    //    };
    //    var actual = tokeClient.CreateTokenAsync(new CreateTokenSubmit { Claims = claims, ExpireMinutes = 30 }).Result;
    //    return actual.Data.Token;
    //}

    [Fact]
    public async Task CreateToken()
    {
        var httpClient = _factory.CreateClient();

        var client = new TokenBuilderHttpClient(httpClient, SecretKey, ClientName, null, null);

        var result = await client.CreateTokenAsync(new CreateTokenSubmit
        {
            Client = ClientName
        });
        Assert.Equal(0, result.Code);
        Assert.NotNull(result.Data.Token);
    }

    [Fact]
    public async Task CreateTicket()
    {
        var httpClient = _factory.CreateClient();

        var client = new TokenBuilderHttpClient(httpClient, SecretKey, ClientName, null, null);

        var result = await client.CreateTicketAsync(new CreateTokenSubmit
        {
            Client = ClientName
        });
        Assert.Equal(0, result.Code);
        Assert.NotNull(result.Data.Ticket);
    }

    [Fact]
    public async Task BuildTokenByTicket()
    {
        var httpClient = _factory.CreateClient();

        var client = new TokenBuilderHttpClient(httpClient, SecretKey, ClientName, null, null);

        // 首先创建一个票据
        var ticketResult = await client.CreateTicketAsync(new CreateTokenSubmit
        {
            Client = ClientName
        });
        Assert.Equal(0, ticketResult.Code);
        Assert.NotNull(ticketResult.Data.Ticket);

        // 使用票据获取Token
        var tokenResult =
            await httpClient.GetAsync($"tokenbuilder/create-token-by-ticket?ticket={ticketResult.Data.Ticket}");

        Assert.True(tokenResult.IsSuccessStatusCode);

        var content = await tokenResult.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<TokenResult<CreateTokenResult>>(content);

        Assert.Equal(0, result.Code);
        Assert.NotNull(result.Data.Token);
    }

    [Fact]
    public async Task BuildClientToken()
    {
        var httpClient = _factory.CreateClient();

        var client = new TokenBuilderHttpClient(httpClient, SecretKey, ClientName, null, null);

        // 创建 ClientInfoSubmit 对象
        var submit = new ClientInfoSubmit
        {
            Client = ClientName,
            ExpireMinute = 30
        };

        var result = await client.GetClientInfo(submit);

        Assert.Equal(0, result.Code);
        Assert.NotNull(result.Data);
        Assert.NotNull(result.Data.Token);
        Assert.NotNull(result.Data.JwtSecretKey);
    }
}