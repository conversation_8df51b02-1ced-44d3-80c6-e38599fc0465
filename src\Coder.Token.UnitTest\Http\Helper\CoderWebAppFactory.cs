﻿using Coder.Token.Clients;
using Coder.Token.Stores;
using Coder.Token.WebApi.Data;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.Token.UnitTest.Http.Helper;

public class CoderWebAppFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
{
    protected override TestServer CreateServer(IWebHostBuilder builder)
    {
        builder.UseStartup<TStartup>();
        var server = base.CreateServer(builder);
        Seed.Init(server.Host.Services, true);
        return server;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        base.ConfigureWebHost(builder);
        builder.ConfigureServices(services =>
        {
            //附加service 放在这个地方。
            services.AddTransient<IClientStore, MockClientStore>();
        });
    }

    protected override IWebHostBuilder CreateWebHostBuilder()
    {
        return WebHost.CreateDefaultBuilder()
            .UseStartup<TStartup>();
    }
}