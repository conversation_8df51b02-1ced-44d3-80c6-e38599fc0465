﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.Token.Stores;
using Coder.Token.ViewModels;

namespace Coder.Token.UnitTest.Http.Helper;

public class MockClientStore : IClientStore
{
    public Task<Client> GetByIdAsync(int id)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<Client>> ListAsync(ClientSearcher searcher)
    {
        throw new NotImplementedException();
    }

    public void AddOrUpdate(Client client)
    {
        throw new NotImplementedException();
    }

    public Task SaveChangeAsync()
    {
        throw new NotImplementedException();
    }

    public Client Get(string clientName)
    {
        return new Client()
        {
            Id = 1,
            Name = clientName,
            SecretKey = "123456",
            Services = new List<string>()
            {
                "Token", "Member", "User"
            }
        };
    }

    public Task<Client> GetAsync(string clientName)
    {
        throw new NotImplementedException();
    }

    public bool Exist(string name, int? excludeId)
    {
        throw new NotImplementedException();
    }

    public void Delete(string name)
    {
        throw new NotImplementedException();
    }

    public Task<int> CountAsync(ClientSearcher searcher)
    {
        throw new NotImplementedException();
    }

    public Client GetById(int submitId)
    {
        throw new NotImplementedException();
    }
}