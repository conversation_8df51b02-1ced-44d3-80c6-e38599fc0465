﻿using Coder.Authentication;
using Coder.ConsulHelper;
using Coder.DataInitial;
using Coder.Token.WebApi;
using Coder.Token.WebApi.Data;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Configuration;
using System.IO;
using System.Text.RegularExpressions;

namespace Coder.Token.UnitTest.Http.Helper;

/// <summary>
/// </summary>
public class TestStartup
{
    private static readonly Regex OriginWithRegex = new("http://localhost:*?", RegexOptions.IgnoreCase);

    /// <summary>
    /// </summary>
    /// <param name="configuration"></param>
    public TestStartup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    /// <summary>
    /// </summary>
    public IConfiguration Configuration { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddMemoryCache().AddResponseCaching();
        services.AddDataInitializer<ClientInit>(); // 添加初始化系统。

        services.AddControllers().AddApplicationPart(typeof(Startup).Assembly);


        services.AddDistributedMemoryCache();

        TokenServiceSetup(services);
        OnConfigDbContext(services);
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    /// <summary>
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
        }

        app.UseCors();
        app.UseRouting().UseResponseCaching();
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
    }

    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    /// <exception cref="ConfigurationErrorsException"></exception>
    private void TokenServiceSetup(IServiceCollection services)
    {
        services.AddCoderJwtAuth(opt =>
        {
            Configuration.GetSection("TokenService").Bind(opt);
            var rsaManager = new RsaManager();
            opt.IssuerSigningKey = rsaManager.GetPrivateKeyPem();

        });
        //token service。管理token
        services.AddTokenService(opt =>
        {
            Configuration.GetSection("TokenService").Bind(opt);


            opt.AddEfStores<ApplicationDbContext>();
        });
    }

    /// <summary>
    ///     Consul 设置
    /// </summary>
    /// <param name="services"></param>
    protected virtual void ConsulSetup(IServiceCollection services)
    {
        services.RegisterToConsul(options =>
        {
            Configuration.GetSection("ConsulOption").Bind(options);
            var serviceId = Environment.GetEnvironmentVariable("SERVICE_ID");
            if (!string.IsNullOrEmpty(serviceId)) options.ServiceId = serviceId;
        });
    }

    /// <summary>
    ///     设置DbConfig
    /// </summary>
    /// <param name="services"></param>
    protected virtual void OnConfigDbContext(IServiceCollection services)
    {
        if (!Directory.Exists("_data")) Directory.CreateDirectory("_data");

        var connection = "Data Source=database.db;";


        services.AddDbContext<ApplicationDbContext>(
            options1 =>
            {
                options1.UseLazyLoadingProxies();


                options1.UseSqlite(connection, action =>
                {
                    action.CommandTimeout(300);
                    action.MigrationsAssembly("Coder.Token.Migrations.Sqlite");
                });
            });
    }
}