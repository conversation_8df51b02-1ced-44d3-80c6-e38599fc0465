﻿{
  "ConnectionStrings": {
    "mysql": "server=mysql;port=3306;database=coder_tokens;user=root;password=*********;CharSet=utf8;",
    "mssql": "Server=localhost\\SQLEXPRESS;Database=zhc_storage;Trusted_Connection=True;"
  },
  "DB_TYPE": "MYSQL",
  "TokenService": {
    "Client": "Member",
    "SecretKey": "123567",
    "ExpireMinutes": 20,
    "Issuer": "Coder-Token-Service",
    "Host": "http://localhost"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Coder": "Debug"
    }
  },
  "ConsulOption": {
    "ConsulServer": "http://consul:8500",
    "ServiceHost": "_HOST_", /* 要注册的服务 url */
    "ServicePort": -1, /* 要注册服务的访问端口 */
    "ServiceName": "coder_token", /* 服务名称，集群的时候采用这个名称来获取服务 */
    "ServiceId": "coder_token-01", /* 服务id 必须唯一，用于记录那个服务提供者出现问题 */
    "Tags": [ "token", "登录服务" ], /* 可选 日志*/
    "HealthCheckUrl": "http://_HOST_:_PORT_/Health/Status",
    "Enable": false  
  },
  "AllowedHosts": "*"
}
