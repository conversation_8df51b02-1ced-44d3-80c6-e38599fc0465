using System.Threading.Tasks;
using Coder.Token.WebApi.Tests.TestInfrastructure;
using FluentAssertions;
using Xunit;

namespace Coder.Token.WebApi.Tests.Controllers;

/// <summary>
/// 简化的控制器测试，用于验证基础测试框架
/// </summary>
public class SimpleControllerTests : TestBase
{
    public SimpleControllerTests(TestWebApplicationFactory factory) : base(factory)
    {
    }

    [Fact]
    public void TestFramework_ShouldBeWorking()
    {
        // Arrange & Act & Assert
        // 这个测试只验证测试框架本身是否工作
        var result = 1 + 1;
        result.Should().Be(2);
    }

    [Fact]
    public void TestDataHelper_ShouldCreateValidData()
    {
        // Arrange & Act
        var tokenSubmit = TestHelpers.TestDataHelper.CreateValidTokenSubmit();

        // Assert
        tokenSubmit.Should().NotBeNull();
        tokenSubmit.Client.Should().NotBeEmpty();
        tokenSubmit.ExpireMinutes.Should().BeGreaterThan(0);
    }

    [Fact]
    public void ApplicationDbContext_ShouldBeConfigured()
    {
        // Arrange & Act & Assert
        DbContext.Should().NotBeNull();
        // 数据库上下文应该已经配置为内存数据库
        DbContext.Database.ProviderName.Should().Contain("InMemory");
    }
} 