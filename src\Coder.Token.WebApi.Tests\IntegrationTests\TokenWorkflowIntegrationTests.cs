//using System.Collections.Generic;
//using System.Linq;
//using System.Net;
//using System.Threading.Tasks;
//using Coder.Authentication;
//using Coder.Token.Clients;
//using Coder.Token.ViewModels;
//using Coder.Token.WebApi.Tests.TestInfrastructure;
//using Coder.Token.WebApi.Tests.TestHelpers;
//using FluentAssertions;
//using Newtonsoft.Json;
//using Xunit;

//namespace Coder.Token.WebApi.Tests.IntegrationTests;

///// <summary>
///// 集成测试：测试完整的Token工作流程
///// </summary>
//public class TokenWorkflowIntegrationTests : TestBase
//{
//    public TokenWorkflowIntegrationTests(TestWebApplicationFactory factory) : base(factory)
//    {
//    }

//    [Fact]
//    public async Task CompleteTokenWorkflow_ShouldWorkCorrectly()
//    {
//        // 1. 创建Token
//        var createTokenRequest = TestDataHelper.CreateValidTokenSubmit();
//        var tokenResult = await Client.PostAsJsonAsync<CreateTokenSubmit, TokenResult<CreateTokenResult>>(
//            "/TokenBuilder/create-token", createTokenRequest);

//        tokenResult.Should().NotBeNull();
//        tokenResult.Success.Should().BeTrue();
//        tokenResult.Data.Token.Should().NotBeEmpty();

//        var token = tokenResult.Data.Token;

//        // 2. 验证Token（使用过时的TokenManager接口进行验证）
//        var validateResult = await Client.GetAsJsonAsync<TokenResult<TokenValidateResult>>(
//            $"/TokenManager/validate-token?token={token}");

//        validateResult.Should().NotBeNull();
//        validateResult.Success.Should().BeTrue();
//        validateResult.Success.Should().BeTrue();

//        // 3. 获取客户端信息
//        var clientInfoRequest = TestDataHelper.CreateValidClientInfoSubmit();
//        var clientInfoResult = await Client.PostAsJsonAsync<ClientInfoSubmit, TokenResult<ClientInfoResult>>(
//            "/TokenBuilder/get-client-info", clientInfoRequest);

//        clientInfoResult.Should().NotBeNull();
//        clientInfoResult.Success.Should().BeTrue();
//        clientInfoResult.Data.Should().NotBeNull();
//    }

//    [Fact]
//    public async Task TicketBasedTokenWorkflow_ShouldWorkCorrectly()
//    {
//        // 1. 创建Ticket
//        var createTicketRequest = TestDataHelper.CreateValidTokenSubmit();
//        var ticketResult = await Client.PostAsJsonAsync<CreateTokenSubmit, TokenResult<CreateTokenTicketResult>>(
//            "/TokenBuilder/create-ticket", createTicketRequest);

//        ticketResult.Should().NotBeNull();
//        ticketResult.Success.Should().BeTrue();
//        ticketResult.Data.Ticket.Should().NotBeEmpty();

//        var ticket = ticketResult.Data.Ticket;

//        // 2. 通过Ticket获取Token
//        var tokenResult = await Client.GetAsJsonAsync<TokenResult<CreateTokenResult>>(
//            $"/TokenBuilder/create-token-by-ticket?ticket={ticket}");

//        tokenResult.Should().NotBeNull();
//        tokenResult.Success.Should().BeTrue();
//        tokenResult.Data.Token.Should().NotBeEmpty();

//        // 3. 验证获取的Token
//        var token = tokenResult.Data.Token;
//        var validateResult = await Client.GetAsJsonAsync<TokenResult<TokenValidateResult>>(
//            $"/TokenManager/validate-token?token={token}");

//        validateResult.Should().NotBeNull();
//        validateResult.Success.Should().BeTrue();
       
//    }

//    [Fact]
//    public async Task UserTokenWorkflow_ShouldWorkCorrectly()
//    {
//        // 1. 创建用户Token
//        var userTokenRequest = TestDataHelper.CreateValidUserTokenSubmit();
//        var tokenResult = await Client.PostAsJsonAsync<BuildUserTokenSubmit, TokenResult<BuildTokenResult>>(
//            "/TokenManager/build-user-token", userTokenRequest);

//        tokenResult.Should().NotBeNull();
//        tokenResult.Success.Should().BeTrue();
//        tokenResult.Data.Token.Should().NotBeEmpty();

//        var token = tokenResult.Data.Token;

//        // 2. 验证Token
//        var validateResult = await Client.GetAsJsonAsync<TokenResult<TokenValidateResult>>(
//            $"/TokenManager/validate-token?token={token}");

//        validateResult.Should().NotBeNull();
//        validateResult.Success.Should().BeTrue();
      
//        // 3. 移除用户Token
//        var removeRequest = TestDataHelper.CreateValidRemoveUserTokenSubmit();
//        var removeResult = await Client.PostAsJsonAsync<RemoveUserTokenSubmit, TokenResult<bool>>(
//            "/TokenManager/remove-user-token", removeRequest);

//        removeResult.Should().NotBeNull();
//        removeResult.Success.Should().BeTrue();
//    }

//    [Fact]
//    public async Task ClientTokenWorkflow_ShouldWorkCorrectly()
//    {
//        // 1. 创建客户端Token
//        var clientTokenRequest = TestDataHelper.CreateValidClientTokenSubmit();
//        var tokenResult = await Client.PostAsJsonAsync<BuildClientTokeSubmit, TokenResult<BuildTokenResult>>(
//            "/TokenManager/build-client-token", clientTokenRequest);

//        tokenResult.Should().NotBeNull();
//        tokenResult.Success.Should().BeTrue();
//        tokenResult.Data.Token.Should().NotBeEmpty();

//        var token = tokenResult.Data.Token;

//        // 2. 验证Token
//        var validateResult = await Client.GetAsJsonAsync<TokenResult<TokenValidateResult>>(
//            $"/TokenManager/validate-token?token={token}");

//        validateResult.Should().NotBeNull();
//        validateResult.Success.Should().BeTrue();
      

//        // 3. 移除客户端Token
//        var removeRequest = TestDataHelper.CreateValidRemoveClientTokenSubmit();
//        var removeResult = await Client.PostAsJsonAsync<RemoveClientTokenSubmit, TokenResult<bool>>(
//            "/TokenManager/remove-client-token", removeRequest);

//        removeResult.Should().NotBeNull();
//        removeResult.Success.Should().BeTrue();
//    }

//    [Fact]
//    public async Task ErrorHandling_ShouldWorkCorrectly()
//    {
//        // 测试无效的客户端ID
//        var invalidRequest = new CreateTokenSubmit
//        {
//            ClientId = "invalid-client-id",
//            ClientSecret = "invalid-secret",
//            ServiceName = "test-service"
//        };

//        var response = await Client.PostAsJsonRawAsync("/TokenBuilder/create-token", invalidRequest);
        
//        // 应该返回错误响应，但HTTP状态码可能是200（业务层错误）
//        if (response.StatusCode == HttpStatusCode.OK)
//        {
//            var content = await response.Content.ReadAsStringAsync();
//            var result = JsonConvert.DeserializeObject<TokenResult<CreateTokenResult>>(content);
//            result.Success.Should().BeFalse();
//        }
//        else
//        {
//            response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.Unauthorized);
//        }
//    }

//    [Fact]
//    public async Task ConcurrentTokenCreation_ShouldWorkCorrectly()
//    {
//        // 并发创建多个Token
//        var request = TestDataHelper.CreateValidTokenSubmit();
        
//        var tasks = new List<Task<TokenResult<CreateTokenResult>>>();
//        for (int i = 0; i < 5; i++)
//        {
//            tasks.Add(Client.PostAsJsonAsync<CreateTokenSubmit, TokenResult<CreateTokenResult>>(
//                "/TokenBuilder/create-token", request));
//        }

//        var results = await Task.WhenAll(tasks);

//        // 所有请求都应该成功
//        foreach (var result in results)
//        {
//            result.Should().NotBeNull();
//            result.Success.Should().BeTrue();
//            result.Data.Token.Should().NotBeEmpty();
//        }

//        // 所有Token应该都不相同（因为包含时间戳等动态信息）
//        var tokens = results.Select(r => r.Data.Token).ToList();
//        tokens.Should().OnlyHaveUniqueItems();
//    }

//    [Fact]
//    public async Task PerformanceTest_TokenCreationShouldBeFast()
//    {
//        var request = TestDataHelper.CreateValidTokenSubmit();
//        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

//        // 创建10个Token并测量时间
//        for (int i = 0; i < 10; i++)
//        {
//            var result = await Client.PostAsJsonAsync<CreateTokenSubmit, TokenResult<CreateTokenResult>>(
//                "/TokenBuilder/create-token", request);
//            result.Success.Should().BeTrue();
//        }

//        stopwatch.Stop();
        
//        // Token创建应该在合理时间内完成（这里假设10个token在5秒内完成）
//        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000);
//    }
//} 