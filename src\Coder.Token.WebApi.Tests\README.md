# Coder.Token.WebApi.Tests

这是 `Coder.Token.WebApi` 项目的单元测试和集成测试项目。

## 项目结构

```
Coder.Token.WebApi.Tests/
├── Controllers/                     # 控制器测试
│   ├── TokenBuilderControllerTests.cs
│   ├── TokenManagerControllerTests.cs
│   └── ClientControllerTests.cs
├── IntegrationTests/               # 集成测试
│   └── TokenWorkflowIntegrationTests.cs
├── TestInfrastructure/             # 测试基础设施
│   ├── TestWebApplicationFactory.cs
│   └── TestBase.cs
├── TestHelpers/                    # 测试辅助工具
│   ├── TestDataHelper.cs
│   └── HttpClientExtensions.cs
├── appsettings.json               # 测试配置
├── appsettings.Test.json          # 专用测试配置
└── Coder.Token.WebApi.Tests.csproj
```

## 功能特性

### 🧪 测试类型

- **单元测试**: 针对各个控制器的API端点测试
- **集成测试**: 完整的业务流程测试
- **性能测试**: 基本的性能指标验证
- **并发测试**: 多线程场景下的稳定性测试

### 🏗️ 测试基础设施

- **自定义 WebApplicationFactory**: 配置测试环境
- **内存数据库**: 使用 Entity Framework InMemory 提供者
- **测试数据种子**: 自动初始化测试数据
- **测试基类**: 提供通用的测试功能和清理机制

### 🛠️ 辅助工具

- **TestDataHelper**: 创建各种测试数据的辅助方法
- **HttpClientExtensions**: 简化HTTP请求的扩展方法
- **FluentAssertions**: 提供更好的断言体验

## 测试覆盖

### TokenBuilderController 测试
- ✅ 创建Token (`create-token`)
- ✅ 创建Ticket (`create-ticket`) 
- ✅ 通过Ticket获取Token (`create-token-by-ticket`)
- ✅ 获取客户端信息 (`get-client-info`)
- ✅ 各种错误场景处理

### TokenManagerController 测试
- ✅ 获取配置 (`config`)
- ✅ 构建用户Token (`build-user-token`)
- ✅ 构建客户端Token (`build-client-token`)
- ✅ 构建Ticket (`build-ticket`)
- ✅ 验证Token (`validate-token`)
- ✅ 移除用户Token (`remove-user-token`)
- ✅ 移除客户端Token (`remove-client-token`)

### ClientController 测试
- ✅ 客户端列表 (`list`, `list-api`)
- ✅ 获取客户端 (`get`)
- ✅ 创建客户端 (`create`)
- ✅ 更新客户端 (`update`)
- ✅ 删除客户端 (`delete`)
- ⚠️ 注意: 需要管理员权限，测试中可能返回 `Unauthorized`

### 集成测试
- ✅ 完整Token工作流程
- ✅ 基于Ticket的Token工作流程
- ✅ 用户Token工作流程
- ✅ 客户端Token工作流程
- ✅ 错误处理测试
- ✅ 并发测试
- ✅ 性能测试

## 运行测试

### 使用 Visual Studio
1. 打开解决方案
2. 在 "测试资源管理器" 中运行测试

### 使用命令行
```bash
# 进入测试项目目录
cd src/Coder.Token.WebApi.Tests

# 运行所有测试
dotnet test

# 运行指定测试类
dotnet test --filter "ClassName=TokenBuilderControllerTests"

# 运行指定测试方法
dotnet test --filter "MethodName=CreateToken_WithValidRequest_ShouldReturnSuccessResult"

# 生成代码覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

### 使用过滤器
```bash
# 只运行集成测试
dotnet test --filter "Category=Integration"

# 只运行单元测试
dotnet test --filter "Category=Unit"

# 运行特定控制器的测试
dotnet test --filter "FullyQualifiedName~TokenBuilderController"
```

## 配置说明

### appsettings.Test.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=:memory:",  // 内存数据库
    "Redis": "localhost:6379"
  },
  "TokenServiceOptions": {
    "Issuer": "TestIssuer",
    "SecretKey": "TestSecretKeyForUnitTesting123456789",
    "EnableCache": false  // 测试时禁用缓存
  }
}
```

## 最佳实践

### 1. 测试命名
- 使用 `MethodName_Scenario_ExpectedResult` 格式
- 例: `CreateToken_WithValidRequest_ShouldReturnSuccessResult`

### 2. 测试结构 (AAA Pattern)
```csharp
[Fact]
public async Task TestMethod()
{
    // Arrange - 准备测试数据
    var request = TestDataHelper.CreateValidTokenSubmit();
    
    // Act - 执行被测试的操作
    var result = await Client.PostAsJsonAsync<CreateTokenSubmit, TokenResult<CreateTokenResult>>(
        "/TokenBuilder/create-token", request);
    
    // Assert - 验证结果
    result.Should().NotBeNull();
    result.Success.Should().BeTrue();
}
```

### 3. 使用辅助工具
```csharp
// 使用 TestDataHelper 创建测试数据
var request = TestDataHelper.CreateValidTokenSubmit();

// 使用 HttpClientExtensions 简化HTTP请求
var result = await Client.PostAsJsonAsync<TRequest, TResponse>(url, request);

// 使用 FluentAssertions 进行断言
result.Should().NotBeNull();
result.Success.Should().BeTrue();
```

### 4. 测试隔离
- 每个测试都应该独立运行
- 使用 `TestBase.CleanupTestData()` 清理测试数据
- 避免测试之间的依赖关系

## 依赖项

- **xUnit**: 测试框架
- **Microsoft.AspNetCore.Mvc.Testing**: ASP.NET Core 集成测试
- **Moq**: 模拟框架
- **FluentAssertions**: 断言库
- **Microsoft.EntityFrameworkCore.InMemory**: 内存数据库
- **Newtonsoft.Json**: JSON 序列化

## 注意事项

1. **身份验证**: `ClientController` 需要管理员权限，测试中可能返回 `Unauthorized`
2. **过时的API**: `TokenManagerController` 已标记为过时，但仍需测试以确保向后兼容性
3. **并发测试**: 某些测试可能在高并发环境下表现不同
4. **性能测试**: 性能阈值可能需要根据实际环境调整

## 扩展测试

要添加新的测试：

1. 在相应的测试类中添加测试方法
2. 使用 `TestDataHelper` 创建测试数据
3. 遵循 AAA 模式编写测试
4. 添加适当的断言
5. 考虑错误场景和边界条件

## 故障排除

### 常见问题

1. **测试数据库连接失败**
   - 检查 `appsettings.Test.json` 配置
   - 确保使用内存数据库

2. **依赖项问题**
   - 运行 `dotnet restore`
   - 检查包引用版本

3. **权限相关测试失败**
   - 某些控制器需要特定权限
   - 测试中会处理 `Unauthorized` 响应

4. **并发测试不稳定**
   - 检查测试数据隔离
   - 确保清理逻辑正确执行 