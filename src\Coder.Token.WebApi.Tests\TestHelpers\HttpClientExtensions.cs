using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Coder.Token.WebApi.Tests.TestHelpers;

/// <summary>
/// HttpClient 扩展方法，简化测试中的HTTP请求
/// </summary>
public static class HttpClientExtensions
{
    /// <summary>
    /// 发送POST请求并返回指定类型的结果
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="client">HttpClient实例</param>
    /// <param name="url">请求URL</param>
    /// <param name="request">请求对象</param>
    /// <returns>响应对象</returns>
    public static async Task<TResponse> PostAsJsonAsync<TRequest, TResponse>(
        this HttpClient client, 
        string url, 
        TRequest request)
    {
        var json = JsonConvert.SerializeObject(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await client.PostAsync(url, content);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        return JsonConvert.DeserializeObject<TResponse>(responseContent);
    }

    /// <summary>
    /// 发送GET请求并返回指定类型的结果
    /// </summary>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="client">HttpClient实例</param>
    /// <param name="url">请求URL</param>
    /// <returns>响应对象</returns>
    public static async Task<TResponse> GetAsJsonAsync<TResponse>(
        this HttpClient client, 
        string url)
    {
        var response = await client.GetAsync(url);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        return JsonConvert.DeserializeObject<TResponse>(responseContent);
    }

    /// <summary>
    /// 发送POST请求并返回原始HttpResponseMessage
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <param name="client">HttpClient实例</param>
    /// <param name="url">请求URL</param>
    /// <param name="request">请求对象</param>
    /// <returns>HttpResponseMessage</returns>
    public static async Task<HttpResponseMessage> PostAsJsonRawAsync<TRequest>(
        this HttpClient client, 
        string url, 
        TRequest request)
    {
        var json = JsonConvert.SerializeObject(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        return await client.PostAsync(url, content);
    }

    /// <summary>
    /// 发送PUT请求并返回指定类型的结果
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="client">HttpClient实例</param>
    /// <param name="url">请求URL</param>
    /// <param name="request">请求对象</param>
    /// <returns>响应对象</returns>
    public static async Task<TResponse> PutAsJsonAsync<TRequest, TResponse>(
        this HttpClient client, 
        string url, 
        TRequest request)
    {
        var json = JsonConvert.SerializeObject(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await client.PutAsync(url, content);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        return JsonConvert.DeserializeObject<TResponse>(responseContent);
    }

    /// <summary>
    /// 发送DELETE请求并返回指定类型的结果
    /// </summary>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="client">HttpClient实例</param>
    /// <param name="url">请求URL</param>
    /// <returns>响应对象</returns>
    public static async Task<TResponse> DeleteAsJsonAsync<TResponse>(
        this HttpClient client, 
        string url)
    {
        var response = await client.DeleteAsync(url);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        return JsonConvert.DeserializeObject<TResponse>(responseContent);
    }

    /// <summary>
    /// 检查响应是否成功并返回内容
    /// </summary>
    /// <param name="response">HttpResponseMessage</param>
    /// <returns>响应内容字符串</returns>
    public static async Task<string> EnsureSuccessAndGetContentAsync(this HttpResponseMessage response)
    {
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }
} 