using System;
using System.Collections.Generic;
using Coder.Authentication;
using Coder.Token.ViewModels;

namespace Coder.Token.WebApi.Tests.TestHelpers;

/// <summary>
/// 测试数据辅助类
/// </summary>
public static class TestDataHelper
{
    /// <summary>
    /// 创建有效的CreateTokenSubmit
    /// </summary>
    /// <returns></returns>
    public static CreateTokenSubmit CreateValidTokenSubmit()
    {
        return new CreateTokenSubmit
        {
            Client = "test-client-1",
            ExpireMinutes = 30,
            Claims = new List<ClaimSubmit>()
        };
    }

    /// <summary>
    /// 创建有效的BuildUserTokenSubmit
    /// </summary>
    /// <returns></returns>
    public static BuildUserTokenSubmit CreateValidUserTokenSubmit()
    {
        return new BuildUserTokenSubmit
        {
            Client = "test-client-1",
            ExpireMinutes = 30,
            Claims = new List<ClaimSubmit>
            {
                new ClaimSubmit { Type = "role", Value = "user" },
                new ClaimSubmit { Type = "department", Value = "IT" }
            }
        };
    }

    /// <summary>
    /// 创建有效的BuildClientTokenSubmit
    /// </summary>
    /// <returns></returns>
    public static BuildClientTokeSubmit CreateValidClientTokenSubmit()
    {
        return new BuildClientTokeSubmit
        {
            Client = "test-client-1",
            Audience = "test-audience"
        };
    }

    /// <summary>
    /// 创建有效的ClientSubmit
    /// </summary>
    /// <returns></returns>
    public static ClientSubmit CreateValidClientSubmit()
    {
        return new ClientSubmit
        {
            Id = 1,
            Name = "新测试客户端",
            SecretKey = "new-test-secret",
            Services = new List<string> { "new-test-service" },
            ClaimBuildType = Coder.Token.ClaimBuildType.Client
        };
    }

    /// <summary>
    /// 创建有效的ClientInfoSubmit
    /// </summary>
    /// <returns></returns>
    public static BuildTokenSubmitBase CreateValidClientInfoSubmit()
    {
        return new BuildTokenSubmitBase
        {
            Client = "test-client-1"
        };
    }

    /// <summary>
    /// 创建有效的RemoveUserTokenSubmit
    /// </summary>
    /// <returns></returns>
    public static RemoveUserTokenSubmit CreateValidRemoveUserTokenSubmit()
    {
        return new RemoveUserTokenSubmit
        {
            Client = "test-client-1"
        };
    }

    /// <summary>
    /// 创建有效的RemoveClientTokenSubmit
    /// </summary>
    /// <returns></returns>
    public static RemoveClientTokenSubmit CreateValidRemoveClientTokenSubmit()
    {
        return new RemoveClientTokenSubmit
        {
            Client = "test-client-1"
        };
    }

    /// <summary>
    /// 创建无效的CreateTokenSubmit（空Client）
    /// </summary>
    /// <returns></returns>
    public static CreateTokenSubmit CreateInvalidTokenSubmit()
    {
        return new CreateTokenSubmit
        {
            Client = "",
            ExpireMinutes = 30,
            Claims = new List<ClaimSubmit>()
        };
    }

    /// <summary>
    /// 创建客户端搜索条件
    /// </summary>
    /// <returns></returns>
    public static ClientSearcher CreateClientSearcher()
    {
        return new ClientSearcher
        {
            Page = 1,
            PageSize = 10,
            Name = ""
        };
    }

    /// <summary>
    /// 生成随机的客户端ID
    /// </summary>
    /// <returns></returns>
    public static string GenerateRandomClientId()
    {
        return $"test-client-{Guid.NewGuid():N}";
    }

    /// <summary>
    /// 生成随机的用户ID
    /// </summary>
    /// <returns></returns>
    public static string GenerateRandomUserId()
    {
        return $"test-user-{Guid.NewGuid():N}";
    }
} 