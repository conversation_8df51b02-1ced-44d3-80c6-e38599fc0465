using System;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Coder.Token;
using Xunit;

namespace Coder.Token.WebApi.Tests.TestInfrastructure;

/// <summary>
/// 测试基类，提供通用的测试功能
/// </summary>
public abstract class TestBase : IClassFixture<TestWebApplicationFactory>, IDisposable
{
    protected readonly TestWebApplicationFactory Factory;
    protected readonly HttpClient Client;
    protected readonly IServiceScope Scope;
    protected readonly ApplicationDbContext DbContext;

    protected TestBase(TestWebApplicationFactory factory)
    {
        Factory = factory;
        Client = factory.CreateClient();
        Scope = factory.Services.CreateScope();
        DbContext = Scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }

    /// <summary>
    /// 清理测试数据
    /// </summary>
    protected virtual void CleanupTestData()
    {
        // 简化的清理逻辑 - 后续可以根据需要添加具体的清理操作
        // DbContext.Database.EnsureDeleted();
    }

    public virtual void Dispose()
    {
        CleanupTestData();
        Client?.Dispose();
        Scope?.Dispose();
    }
} 