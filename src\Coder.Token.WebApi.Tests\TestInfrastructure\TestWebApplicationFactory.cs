using System;
using System.Linq;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Coder.Token;
using Microsoft.Extensions.Logging;

namespace Coder.Token.WebApi.Tests.TestInfrastructure;

/// <summary>
/// 自定义的 WebApplicationFactory 用于集成测试
/// </summary>
public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.SetBasePath(System.IO.Directory.GetCurrentDirectory())
                  .AddJsonFile("appsettings.Test.json", optional: false, reloadOnChange: true)
                  .AddEnvironmentVariables();
        });

        builder.ConfigureServices(services =>
        {
            // 移除原有的 DbContext 注册
            var descriptor = services.FirstOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // 使用内存数据库进行测试
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDatabase");
            });

            // 为测试环境添加模拟的服务
            // 这可以避免真实的网络调用
            services.AddSingleton<Microsoft.Extensions.Configuration.IConfiguration>(provider =>
            {
                var configuration = new Microsoft.Extensions.Configuration.ConfigurationBuilder()
                    .AddJsonFile("appsettings.Test.json")
                    .Build();
                return configuration;
            });
        });

        builder.UseEnvironment("Testing");
    }
} 