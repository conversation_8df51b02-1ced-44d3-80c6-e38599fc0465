{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Data Source=:memory:", "Redis": "localhost:6379"}, "TokenServiceOptions": {"Issuer": "<PERSON><PERSON><PERSON><PERSON>", "Audience": "TestAudience", "SecretKey": "TestSecretKeyForUnitTesting123456789", "ExpireMinutes": 30, "RefreshTokenExpireDays": 7, "AllowMultipleLogin": true, "EnableCache": false}, "DataProvider": "InMemory", "CoderAuthentication": {"TokenServiceAddress": "", "JwtSecretKey": "TestSecretKeyForUnitTesting123456789", "Issuer": "<PERSON><PERSON><PERSON><PERSON>", "Audience": "TestAudience", "ClientId": "test-client", "ClientSecret": "test-secret"}}