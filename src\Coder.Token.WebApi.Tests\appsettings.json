{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=:memory:", "Redis": "localhost:6379"}, "TokenServiceOptions": {"Issuer": "<PERSON><PERSON><PERSON><PERSON>", "Audience": "TestAudience", "SecretKey": "TestSecretKeyForUnitTesting123456789", "ExpireMinutes": 30, "RefreshTokenExpireDays": 7, "AllowMultipleLogin": true}, "DataProvider": "InMemory"}