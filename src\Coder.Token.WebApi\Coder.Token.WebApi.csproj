<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Version>9.1.4</Version>
		<Copyright>珠海酷迪技术有限公司@2018-2025</Copyright>
		<GeneratePackageOnBuild>false</GeneratePackageOnBuild>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.8" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0">
		  <TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Coder.ConsulHelper" Version="2.6.0" />
		<PackageReference Include="Coder.HealthChecks" Version="1.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
		<PackageReference Include="Coder.DataInitial" Version="1.1.6" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
		<PackageReference Include="NLog.Targets.Loki" Version="2.2.0">
		  <TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="NLog" Version="6.0.2">			
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
		<PackageReference Include="NLog.Web.AspNetCore" Version="6.0.2" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="logs\" />
		<Folder Include="Migrations\" />
		<Folder Include="Modules\" />
		<Folder Include="rsa\" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Coder.Authentication\Coder.Authentication.csproj" />
		<ProjectReference Include="..\Coder.Token.Core\Coder.Token.Core.csproj" />
		<ProjectReference Include="..\Coder.Token.HttpClients\Coder.Token.HttpClients.csproj" />
		<ProjectReference Include="..\Coder.Token.Migrations.DM\Coder.Token.Migrations.DM.csproj" />
		<ProjectReference Include="..\Coder.Token.Migrations.Mssql\Coder.Token.Migrations.Mssql.csproj" />
		<ProjectReference Include="..\Coder.Token.Migrations.Mysql\Coder.Token.Migrations.Mysql.csproj" />
		<ProjectReference Include="..\Coder.Token.Migrations.Sqlite\Coder.Token.Migrations.Sqlite.csproj" />
		<ProjectReference Include="..\Coder.Token.Stores.Ef\Coder.Token.Stores.Ef.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Content Update="Properties\launchSettings.json">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ProjectExtensions>
		<VisualStudio>
			<UserProperties appsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>
</Project>