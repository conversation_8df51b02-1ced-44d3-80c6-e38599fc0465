<?xml version="1.0"?>

<doc>
	<assembly>
		<name>Coder.Token.WebApi</name>
	</assembly>
	<members>
		<member name="T:Coder.Token.WebApi.Controllers.ClientController">
			<summary>
				客户端设置
			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.#ctor(Coder.Token.Stores.IClientStore)">
			<summary>
			</summary>
			<param name="clientStore"></param>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.List(Coder.Token.ViewModels.ClientSearcher)">
			<summary>
				客户端列表
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.Count(Coder.Token.ViewModels.ClientSearcher)">
			<summary>
				数据 配套 客户端列表
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.Get(System.String)">
			<summary>
				获取客户端
			</summary>
			<param name="name"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.Exist(System.String,System.Nullable{System.Int32})">
			<summary>
				检查客户端是否存在
			</summary>
			<param name="name"></param>
			<param name="excludeId"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.Delete(System.String)">
			<summary>
				删除客户端
			</summary>
			<param name="name"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.Token.WebApi.Controllers.ClientController.Save(Coder.Token.ViewModels.ClientSubmit)">
			<summary>
				保存客户端
			</summary>
			<param name="submit"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.Token.WebApi.Controllers.TokenManagerController">
			<summary>
				TokenManager,用于验证/生成token，需要配置Client对象。
			</summary>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.#ctor(Microsoft.Extensions.Logging.ILogger{Coder.Token.WebApi.Controllers.TokenManagerController})">
			<summary>
			</summary>
			<param name="logger"></param>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.Build(Coder.Authentication.BuildTokenSubmit,Coder.Token.TokenBuildingManager,Coder.Token.TokenValidateManager)">
			<summary>
				通过User数据生成token
			</summary>
			<param name="submit"></param>
			<param name="tokenBuildingManager"></param>
			<param name="validateManager"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.BuildToken(System.String,Coder.Token.TokenBuildingManager,Coder.Token.TokenValidateManager)">
			<summary>
				通过ticket创建token。
			</summary>
			<param name="ticket"></param>
			<param name="tokenBuildingManager"></param>
			<param name="validateManager"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.Verify(System.String,Coder.Token.TokenValidateManager)">
			<summary>
				验证token，如果token生效时间少于1/4，那么回返回一个新的token。
			</summary>
			<param name="token"></param>
			<param name="validateManager"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.BuildAudienceToken(Coder.Token.ViewModels.ClientTokeSubmit,Coder.Token.Stores.IClientStore,Coder.Token.TokenBuildingManager)">
			<summary>
				创建Audience的token
			</summary>
			<param name="submit"></param>
			<param name="store"></param>
			<param name="tokenBuilderManager"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.ExpireAudienceToken(Coder.Token.ViewModels.RemoveClientTokenSubmit,Coder.Token.TokenValidateManager)">
			<summary>
				移除 token
			</summary>
			<param name="submit"></param>
			<param name="validateManager"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.ExpireUserToken(Coder.Token.ViewModels.RemoveUserTokenSubmit,Coder.Token.TokenValidateManager)">
			<summary>
				移除cache中的token
			</summary>
			<param name="submit"></param>
			<param name="validateManager"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.Token.WebApi.Controllers.TokenManagerController.BuildTokenTicket(Coder.Authentication.BuildTokenSubmit,Coder.Token.TokenBuildingManager)">
			<summary>
				创建token的ticket，通过这个ticket可以规定时间内换取token一次。
			</summary>
			<returns></returns>
		</member>
		<member name="T:Coder.Token.WebApi.Data.ClientInit">
			<summary>

			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Data.ClientInit.#ctor(Coder.Token.Stores.IClientStore)">
			<summary>

			</summary>
			<param name="store"></param>
		</member>
		<member name="M:Coder.Token.WebApi.Data.ClientInit.Run">
			<summary>

			</summary>
		</member>
		<member name="T:Coder.Token.WebApi.Data.Seed">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Data.Seed.Init(System.IServiceProvider,System.Boolean)">
			<summary>
			</summary>
			<param name="sp"></param>
			<param name="dropAndCreate">tru 用于测试，每次调用都会drop table</param>
		</member>
		<member name="T:Coder.Token.WebApi.Filtters.CoderBadRequest">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Filtters.CoderBadRequest.#ctor(Microsoft.AspNetCore.Mvc.ActionContext)">
			<summary>

			</summary>
			<param name="context"></param>
		</member>
		<member name="P:Coder.Token.WebApi.Filtters.CoderBadRequest.Errors">
			<summary>

			</summary>
		</member>
		<member name="P:Coder.Token.WebApi.Filtters.CoderBadRequest.Message">
			<summary>

			</summary>
		</member>
		<member name="P:Coder.Token.WebApi.Filtters.CoderBadRequest.Success">
			<summary>

			</summary>
		</member>
		<member name="T:Coder.Token.WebApi.Filtters.CustomExceptionFilterAttribute">
			<summary>

			</summary>
		</member>
		<member
			name="M:Coder.Token.WebApi.Filtters.CustomExceptionFilterAttribute.#ctor(Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider,Microsoft.Extensions.Logging.ILogger{Coder.Token.WebApi.Filtters.CustomExceptionFilterAttribute})">
			<summary>

			</summary>
			<param name="hostingEnvironment"></param>
			<param name="modelMetadataProvider"></param>
			<param name="logger"></param>
		</member>
		<member
			name="M:Coder.Token.WebApi.Filtters.CustomExceptionFilterAttribute.OnException(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
			<summary>

			</summary>
			<param name="context"></param>
		</member>
		<member name="T:Coder.Token.WebApi.Job.BlackListCleanupHostService">
			<summary>

			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Job.BlackListCleanupHostService.#ctor(System.IServiceProvider)">
			<summary>

			</summary>
			<param name="serviceProvider"></param>
		</member>
		<member name="M:Coder.Token.WebApi.Job.BlackListCleanupHostService.StartAsync(System.Threading.CancellationToken)">
			<summary>

			</summary>
			<param name="cancellationToken"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.Token.WebApi.Job.BlackListCleanupHostService.ClearUp(System.Object)">
			<summary>

			</summary>
			<param name="state"></param>
		</member>
		<member name="M:Coder.Token.WebApi.Job.BlackListCleanupHostService.StopAsync(System.Threading.CancellationToken)">
			<summary>

			</summary>
			<param name="cancellationToken"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.Token.WebApi.Program">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Program.Main(System.String[])">
			<summary>
			</summary>
			<param name="args"></param>
		</member>
		<member name="M:Coder.Token.WebApi.Program.CreateHostBuilder(System.String[])">
			<summary>
			</summary>
			<param name="args"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.Token.WebApi.Startup">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.Token.WebApi.Startup.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
			<summary>
			</summary>
			<param name="configuration"></param>
		</member>
		<member name="P:Coder.Token.WebApi.Startup.Configuration">
			<summary>
			</summary>
		</member>
		<member
			name="M:Coder.Token.WebApi.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
			</summary>
			<param name="services"></param>
		</member>
		<member
			name="M:Coder.Token.WebApi.Startup.Configure(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
			<summary>
			</summary>
			<param name="app"></param>
			<param name="env"></param>
		</member>
		<member
			name="M:Coder.Token.WebApi.Startup.TokenServiceSetup(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
			</summary>
			<param name="services"></param>
			<exception cref="T:System.Configuration.ConfigurationErrorsException"></exception>
		</member>
		<member name="M:Coder.Token.WebApi.Startup.ConsulSetup(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
				Consul 设置
			</summary>
			<param name="services"></param>
		</member>
		<member
			name="M:Coder.Token.WebApi.Startup.OnConfigDbContext(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
				设置DbConfig
			</summary>
			<param name="services"></param>
		</member>
	</members>
</doc>