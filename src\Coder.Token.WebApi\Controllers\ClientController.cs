﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.Token.Clients;
using Coder.Token.Stores;
using Coder.Token.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.Token.WebApi.Controllers;

/// <summary>
///     客户端设置
/// </summary>
[ApiController]
[Route("[controller]")]
[Authorize(Roles = "admin")]
public class ClientController : Controller
{
    private readonly IClientStore _clientStore;
    private readonly ILogger<ClientController> _logger;

    /// <summary>
    /// </summary>
    /// <param name="clientStore"></param>
    /// <param name="logger"></param>
    public ClientController(IClientStore clientStore, ILogger<ClientController> logger)
    {
        _clientStore = clientStore;
        _logger = logger;
    }

    /// <summary>
    ///     客户端列表
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("list")]
    [ProducesDefaultResponseType(typeof(TokenResult<IEnumerable<ClientListItem>>))]
    public TokenResult<IEnumerable<ClientListItem>> List([FromQuery] ClientSearcher searcher)
    {
        var result = _clientStore.ListAsync(searcher).Result.Select(_ => _.ToViewModel());
        return result.ToSuccessTokenResult("成功获取。");
    }

    /// <summary>
    ///     数据 配套 客户端列表
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("Count")]
    [ProducesDefaultResponseType(typeof(TokenResult<int>))]
    public TokenResult<int> Count([FromQuery] ClientSearcher searcher)
    {
        var client = _clientStore.CountAsync(searcher).Result;
        return client.ToSuccessTokenResult("成功获取");
    }

    /// <summary>
    ///     获取客户端
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [HttpGet("{name}")]
    [ProducesDefaultResponseType(typeof(TokenResult<Client>))]
    public async Task<TokenResult<Client>> Get([FromRoute] string name)
    {
        var reus = await _clientStore.GetAsync(name);
        return reus.ToSuccessTokenResult("成功获取。");
    }

    /// <summary>
    ///     检查客户端是否存在
    /// </summary>
    /// <param name="name"></param>
    /// <param name="excludeId"></param>
    /// <returns></returns>
    [HttpGet("exist/{name}")]
    [ProducesDefaultResponseType(typeof(bool))]
    public TokenResult<bool> Exist([FromRoute] string name, int? excludeId)
    {
        var exist = _clientStore.Exist(name, excludeId);
        return exist.ToSuccessTokenResult("成功获取");
    }

    /// <summary>
    ///     删除客户端
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [HttpDelete("{name}")]
    [ProducesDefaultResponseType(typeof(TokenResult))]
    public TokenResult Delete([FromRoute] string name)
    {
        _clientStore.Delete(name);
        _clientStore.SaveChangeAsync().Wait();
        return new TokenResult("删除成功。", 0);
    }

    /// <summary>
    ///     保存客户端
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    [HttpPost("save")]
    [ProducesDefaultResponseType(typeof(TokenResult<int>))]
    public TokenResult<int> Save([FromBody] ClientSubmit submit)
    {
        var client = _clientStore.GetById(submit.Id) ?? new Client();
        submit.FillTo(client);
        _clientStore.AddOrUpdate(client);
        _clientStore.SaveChangeAsync().Wait();
        return new TokenResult<int>("成功保存。", 0)
        {
            Data = client.Id
        };
    }
}