﻿using System;
using System.Threading.Tasks;
using Coder.Authentication;
using Coder.Token.Clients;
using Coder.Token.Jwt;
using Coder.Token.ViewModels;
using Coder.WebRequestVerifier;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.Token.WebApi.Controllers;

/// <summary>
///     TokenManager,用于验证/生成token，需要配置Client对象。
/// </summary>
[ApiController]
[Route("[controller]")]
[AllowAnonymous]
public class TokenBuilderController : ControllerBase
{
    private readonly ILogger<TokenBuilderController> _logger;
    private readonly TokenManager _tokenManager;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="tokenManager"></param>
    public TokenBuilderController(ILogger<TokenBuilderController> logger, TokenManager tokenManager)
    {
        _logger = logger;
        _tokenManager = tokenManager;
    }

    [HttpPost("create-token")]
    [ProducesDefaultResponseType(typeof(TokenResult<CreateTokenResult>))]
    [RequestVerifier(typeof(BuildTokenRequestVerifier<CreateTokenSubmit>))]
    public async Task<TokenResult<CreateTokenResult>> CreateToken([FromBody] CreateTokenSubmit submit)
    {
        var result = await _tokenManager.CreateTokenAsync(submit);
        return result;
    }
    /// <summary>
    /// 创建换取token的票据。完成后通过 <seealso cref="BuildTokenByTicket"/> 获取token。
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    [HttpPost("create-ticket")]
    [ProducesDefaultResponseType(typeof(TokenResult<CreateTokenTicketResult>))]
    [RequestVerifier(typeof(BuildTokenRequestVerifier<CreateTokenSubmit>))]
    public async Task<TokenResult<CreateTokenTicketResult>> CreateTicket([FromBody] CreateTokenSubmit submit)
    {
        var result = await _tokenManager.CreateTicketAsync(submit);
        return result;
    }

    /// <summary>
    ///     通过ticket创建token。
    /// </summary>
    /// <param name="ticket"></param>
    /// <returns></returns>
    [HttpGet("create-token-by-ticket")]
    [ProducesDefaultResponseType(typeof(TokenResult<CreateTokenResult>))]
    [AllowAnonymous]
    public async Task<TokenResult<CreateTokenResult>> BuildTokenByTicket([FromQuery] string ticket)
    {
        var user = await _tokenManager.CreateTokenByTicketAsync(ticket);
        
        _logger.LogDebug($"创建token{(user.Success ? "成功" : "失败")}。");
        return user;
    }

    /// <summary>
    ///     获取Client信息。
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    [HttpPost("get-client-info")]
    [RequestVerifier(typeof(BuildTokenRequestVerifier<ClientInfoSubmit>))]
    public async Task<TokenResult<ClientInfoResult>> BuildClientToken([FromBody] ClientInfoSubmit submit)
    {
        var result = await _tokenManager.GetClientInfo(submit);
        return result;
    }   /// <summary>
    ///     移除cache中的token
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="validateManager"></param>
    /// <returns></returns>
    [HttpPut("remove-token")]
    [RequestVerifier(typeof(BuildTokenRequestVerifier<ClientTokeSubmit>))]
    public TokenResult ExpireUserToken([FromBody] RemoveUserTokenSubmit submit,
        [FromServices] TokenValidateManager validateManager)
    {
        var token = JwtValidator.GetJwtTokeFrom(submit.Token);
        var userName = TokenValidateManager.GetUserName(token.Claims);
        var expireTime = TokenValidateManager.GetExpireTime(token.Claims);

        if (string.IsNullOrEmpty(userName))
            return new TokenResult("token被移除", 0);

        if (!string.Equals(userName, submit.UserName, StringComparison.CurrentCultureIgnoreCase))
        {
            _logger.LogWarning(new EventId(900), "没有权限处理不属于自己的token.");
            return new TokenResult(submit.UserName + "没有权限处理不属于自己的token.", -10);
        }

        if (DateTime.Now >= expireTime) return new TokenResult("token被移除", 0);
        _logger.LogWarning(new EventId(900), "设置到黑名单。");
        
        // 使用Fire-and-forget模式，避免阻塞请求
        _ = Task.Run(async () =>
        {
            try
            {
                await validateManager.SetToBlackListAsync(submit.Token, userName, expireTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加token到黑名单失败");
            }
        });

        return new TokenResult("token被移除", 0);
    }
}