# 数据库迁移指南

## 概述

本文档描述了如何安全地迁移现有的客户端数据，从硬编码密钥到环境变量密钥。

## 迁移步骤

### 1. 备份现有数据

在开始迁移前，请备份您的数据库：

```sql
-- 备份客户端表
CREATE TABLE client_backup AS SELECT * FROM token_client;
```

### 2. 设置环境变量

根据现有客户端数据设置环境变量。您可以使用以下SQL查询获取当前客户端列表：

```sql
SELECT Name, Description, SecretKey FROM token_client;
```

**重要：不要在生产环境中继续使用现有的简单密钥！**

### 3. 生成新的安全密钥

为每个客户端生成新的强密钥：

```bash
# 为每个客户端生成新密钥
CLIENT_SECRET_MEMBER=$(openssl rand -base64 32)
CLIENT_SECRET_USER=$(openssl rand -base64 32)
CLIENT_SECRET_GATEWAY=$(openssl rand -base64 32)
# ... 其他客户端
```

### 4. 更新客户端配置

通知各个客户端系统更新其使用的密钥。

### 5. 验证迁移

1. 启动新版本的服务
2. 确认客户端表数据正确更新
3. 测试各个客户端的身份验证

## 密钥轮换策略

### 零停机迁移

如果需要零停机迁移：

1. **阶段1**：部署支持双重密钥验证的版本
2. **阶段2**：逐步更新客户端使用新密钥
3. **阶段3**：验证所有客户端已更新
4. **阶段4**：移除旧密钥支持

### 回滚计划

如果迁移出现问题：

1. 恢复环境变量为原始值
2. 重启服务
3. 如果需要，从备份恢复数据库

## 客户端更新清单

请确保以下客户端已更新密钥：

- [ ] Member（会员系统）
- [ ] User（用户系统）
- [ ] Gateway（网关）
- [ ] OTP（一次性密码）
- [ ] Wechat（微信）
- [ ] Swf（工作流）
- [ ] Fs（文件系统）
- [ ] Notify（通知系统）
- [ ] Org（组织）

## 监控和验证

### 监控指标

迁移后监控以下指标：

1. 身份验证成功率
2. 错误日志中的密钥相关错误
3. 客户端连接状态

### 验证脚本

可以使用以下脚本验证客户端身份验证：

```bash
# 测试客户端身份验证
curl -X POST "http://your-service/tokenbuilder/get-client-info" \
  -H "Content-Type: application/json" \
  -d '{"Client": "Member"}' \
  -H "X-Signature: $(echo -n '{"Client": "Member"}'$CLIENT_SECRET_MEMBER | md5sum | cut -d' ' -f1 | tr '[:lower:]' '[:upper:]')"
```

## 安全注意事项

1. **永远不要在日志中记录密钥**
2. **定期轮换密钥**
3. **使用强密钥（至少32字符）**
4. **确保密钥在传输和存储时都是加密的**
5. **限制密钥访问权限**

## 故障排除

### 常见问题

1. **客户端身份验证失败**
   - 检查客户端是否使用了新密钥
   - 验证环境变量设置正确

2. **服务启动时密钥错误**
   - 确认所有必需的环境变量已设置
   - 检查环境变量值中是否包含特殊字符

3. **数据库连接问题**
   - 验证数据库连接字符串
   - 确认数据库服务正常运行

### 紧急联系方式

如果在迁移过程中遇到严重问题，请联系：

- 系统管理员：[联系信息]
- 开发团队：[联系信息]
- 值班工程师：[联系信息]