﻿using System;
using System.Collections.Generic;
using Coder.DataInitial;
using Coder.Token.Stores;

namespace Coder.Token.WebApi.Data;

/// <summary>
/// 客户端初始化（已过时）
/// </summary>

public class ClientInit : DataInitialize
{
    private readonly IClientStore _store;

    /// <summary>
    /// </summary>
    /// <param name="store"></param>
    public ClientInit(IClientStore store)
    {
        _store = store;
    }

    /// <summary>
    /// </summary>
    public override void Run()
    {
        var member = _store.Get("Member") ?? new Client
        {
            Name = "Member",
            Description = "会员系统",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token"
            },
            ClaimBuildType = ClaimBuildType.Both
        };

        var user = _store.Get("User") ?? new Client
        {
            Name = "User",
            Description = "会员",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token","Member","Notify","OAContract","ScriptWorkflow"
            },
            ClaimBuildType = ClaimBuildType.Customs
        };


        var otp = _store.Get("OTP") ?? new Client
        {
            Name = "OTP",
            Description = "OTP",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token", "Member"
            }
        };

        var gateway = _store.Get("Gateway") ?? new Client
        {
            Name = "Gateway",
            Description = "网关",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token"
            }
        };

        var wechat = _store.Get("Wechat") ?? new Client
        {
            Name = "Wechat",
            Description = "微信",
            SecretKey = "123456",
            Services = new List<string>
            {
                "Token",
                "Member"
            }
        };
        var swf = _store.Get("Swf") ?? new Client
        {
            Name = "ScriptWorkflow",
            Description = "工作流",
            SecretKey = "316",
            Services = new List<string>
            {
                "Token",
                "Member",
                "fs"
            }
        };

        var fs = _store.Get("fs") ?? new Client
        {
            Name = "fs",
            Description = "文件系统",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token",
                "Member"
            }
        };

        var token = _store.Get("Token") ?? new Client
        {
            Name = "Token",
            Description = "Token",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token",
            }
        };

        var notify = _store.Get("Notify") ?? new Client
        {
            Name = "Notify",
            Description = "通知系统",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token",
                "Member"
            }
        };


        var org = _store.Get("Org") ?? new Client
        {
            Name = "Org",
            Description = "组织",
            SecretKey = "123467",
            Services = new List<string>
            {
                "Token",
                "Member",
                "ScriptWorkflow"
            }
        };
        _store.AddOrUpdate(org);
        _store.AddOrUpdate(notify);
        _store.AddOrUpdate(wechat);
        _store.AddOrUpdate(member);
        _store.AddOrUpdate(otp);
        _store.AddOrUpdate(gateway);
        _store.AddOrUpdate(fs);
        _store.AddOrUpdate(swf);
        _store.AddOrUpdate(user);
        _store.AddOrUpdate(token);
        _store.SaveChangeAsync().Wait();
    }
}