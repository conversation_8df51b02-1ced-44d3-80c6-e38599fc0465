# 请参阅 https://aka.ms/customizecontainer 以了解如何自定义调试容器，以及 Visual Studio 如何使用此 Dockerfile 生成映像以更快地进行调试。

# 此阶段用于在快速模式(默认为调试配置)下从 VS 运行时
FROM zhcoder-docker-registry.com:8000/library/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080


# 此阶段用于生成服务项目
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["nuget.config", "."]
COPY ["Coder.Token.WebApi/Coder.Token.WebApi.csproj", "Coder.Token.WebApi/"]
COPY ["Coder.Authentication/Coder.Authentication.csproj", "Coder.Authentication/"]
COPY ["Coder.Token.Abstractions/Coder.Token.Abstractions.csproj", "Coder.Token.Abstractions/"]
COPY ["Coder.Token.Core/Coder.Token.Core.csproj", "Coder.Token.Core/"]
COPY ["Coder.Token.Migrations.DM/Coder.Token.Migrations.DM.csproj", "Coder.Token.Migrations.DM/"]
COPY ["Coder.Token.Stores.Ef/Coder.Token.Stores.Ef.csproj", "Coder.Token.Stores.Ef/"]
COPY ["Coder.Token.Migrations.Mssql/Coder.Token.Migrations.Mssql.csproj", "Coder.Token.Migrations.Mssql/"]
COPY ["Coder.Token.Migrations.Mysql/Coder.Token.Migrations.Mysql.csproj", "Coder.Token.Migrations.Mysql/"]
COPY ["Coder.Token.Migrations.Sqlite/Coder.Token.Migrations.Sqlite.csproj", "Coder.Token.Migrations.Sqlite/"]
RUN dotnet restore "./Coder.Token.WebApi/Coder.Token.WebApi.csproj" --configfile="./nuget.config"
COPY . .
WORKDIR "/src/Coder.Token.WebApi"
RUN dotnet build "./Coder.Token.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/build

# 此阶段用于发布要复制到最终阶段的服务项目
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Coder.Token.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# 此阶段在生产中使用，或在常规模式下从 VS 运行时使用(在不使用调试配置时为默认值)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Coder.Token.WebApi.dll"]