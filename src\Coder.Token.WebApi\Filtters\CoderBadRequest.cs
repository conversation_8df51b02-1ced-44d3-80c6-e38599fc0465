﻿using Microsoft.AspNetCore.Mvc;

namespace Coder.Token.WebApi.Filtters;

/// <summary>
/// </summary>
public sealed class CoderBadRequest
{
    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    public CoderBadRequest(ActionContext context)
    {
        var errors = new SerializableError(context.ModelState);
        Errors = errors;
    }

    /// <summary>
    /// </summary>
    public object Errors { get; set; }

    /// <summary>
    /// </summary>
    public string Message { get; set; } = "提交的数据中出现了错误，请修正后再上传.";

    /// <summary>
    /// </summary>
    public bool Success { get; set; } = false;
}