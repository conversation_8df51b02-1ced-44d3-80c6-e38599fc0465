﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;

namespace Coder.Token.WebApi.Filtters;

/// <summary>
/// 自定义异常过滤器
/// </summary>
public class CustomExceptionFilterAttribute : ExceptionFilterAttribute
{
    private readonly IWebHostEnvironment _hostingEnvironment;
    private readonly ILogger<CustomExceptionFilterAttribute> _logger;
    private readonly IModelMetadataProvider _modelMetadataProvider;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="hostingEnvironment"></param>
    /// <param name="modelMetadataProvider"></param>
    /// <param name="logger"></param>
    public CustomExceptionFilterAttribute(
        IWebHostEnvironment hostingEnvironment,
        IModelMetadataProvider modelMetadataProvider, 
        ILogger<CustomExceptionFilterAttribute> logger)
    {
        _hostingEnvironment = hostingEnvironment ?? throw new ArgumentNullException(nameof(hostingEnvironment));
        _modelMetadataProvider = modelMetadataProvider ?? throw new ArgumentNullException(nameof(modelMetadataProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 异常处理
    /// </summary>
    /// <param name="context"></param>
    public override void OnException(ExceptionContext context)
    {
        // 记录异常详情
        _logger.LogError(context.Exception, "请求处理过程中发生异常。路径: {Path}, 方法: {Method}", 
            context.HttpContext.Request.Path, 
            context.HttpContext.Request.Method);

        // 根据环境设置不同的响应
        if (_hostingEnvironment.IsDevelopment())
        {
            // 开发环境返回详细异常信息
            context.HttpContext.Response.StatusCode = 500;
            context.Result = new JsonResult(new
            {
                error = "Internal Server Error",
                message = context.Exception.Message,
                stackTrace = context.Exception.StackTrace,
                type = context.Exception.GetType().Name
            });
        }
        else
        {
            // 生产环境返回通用错误信息
            context.HttpContext.Response.StatusCode = 500;
            context.Result = new JsonResult(new
            {
                error = "Internal Server Error",
                message = "服务器内部错误，请稍后重试"
            });
        }

        // 标记异常已处理
        context.ExceptionHandled = true;
    }
}