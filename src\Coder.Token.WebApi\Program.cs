using System;
using Coder.Token.WebApi.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NLog;
using NLog.Web;

namespace Coder.Token.WebApi;

/// <summary>
/// </summary>
public class Program
{
    /// <summary>
    /// </summary>
    /// <param name="args"></param>
    public static void Main(string[] args)
    {
        var logger = LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
        try
        {
            var build = CreateHostBuilder(args).Build();

            using var scope = build.Services.CreateScope();
            Seed.Init(scope.ServiceProvider);

            build.Run();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Stopped program because of exception");

            throw;
        }
        finally
        {
            LogManager.Shutdown();
        }
    }


    /// <summary>
    /// </summary>
    /// <param name="args"></param>
    /// <returns></returns>
    public static IHostBuilder CreateHostBuilder(string[] args)
    {
        return Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder
                    .UseNLog()
                    .UseStartup<Startup>();
            });
    }
}