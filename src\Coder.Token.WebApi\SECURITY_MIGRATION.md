# 安全迁移指南

## 概述

本文档描述了如何从硬编码密钥迁移到安全的密钥管理方案。

## 🚨 重要安全提醒

**在生产环境部署前，必须完成以下安全配置！**

## 迁移步骤

### 1. 环境变量配置

将以下环境变量添加到您的系统中：

```bash
# 会员系统密钥（请使用强密钥替换）
export CLIENT_SECRET_MEMBER="your-very-secure-member-secret-key-here"

# 用户密钥
export CLIENT_SECRET_USER="your-very-secure-user-secret-key-here"

# 网关密钥
export CLIENT_SECRET_GATEWAY="your-very-secure-gateway-secret-key-here"

# OTP密钥
export CLIENT_SECRET_OTP="your-very-secure-otp-secret-key-here"

# 其他客户端密钥...
```

### 2. Docker 环境配置

在 `docker-compose.yml` 中：

```yaml
version: '3.8'
services:
  token-service:
    environment:
      - CLIENT_SECRET_MEMBER=${CLIENT_SECRET_MEMBER}
      - CLIENT_SECRET_USER=${CLIENT_SECRET_USER}
      - CLIENT_SECRET_GATEWAY=${CLIENT_SECRET_GATEWAY}
      - CLIENT_SECRET_OTP=${CLIENT_SECRET_OTP}
      # 其他环境变量...
```

### 3. Kubernetes 配置

创建 Secret：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: token-service-secrets
type: Opaque
data:
  CLIENT_SECRET_MEMBER: <base64-encoded-secret>
  CLIENT_SECRET_USER: <base64-encoded-secret>
  # 其他密钥...
```

在 Deployment 中引用：

```yaml
spec:
  containers:
  - name: token-service
    envFrom:
    - secretRef:
        name: token-service-secrets
```

### 4. 验证迁移

1. 确保所有环境变量已设置
2. 启动应用程序
3. 检查日志确认没有"未找到密钥"的错误
4. 测试客户端身份验证功能

## 密钥生成建议

### 强密钥生成

使用以下方法生成强密钥：

```bash
# 使用 openssl
openssl rand -base64 32

# 使用 uuid
uuidgen

# 使用 PowerShell (Windows)
[System.Guid]::NewGuid().ToString()
```

### 密钥轮换

建议定期轮换客户端密钥：

1. 生成新密钥
2. 更新环境变量
3. 重启服务
4. 通知相关客户端更新密钥

## 安全检查清单

- [ ] 所有客户端密钥已从代码中移除
- [ ] 环境变量已正确配置
- [ ] 生产环境使用强密钥（至少32字符）
- [ ] 敏感信息不再输出到日志
- [ ] 密钥管理流程已建立
- [ ] 应急密钥轮换程序已准备

## 故障排除

### 常见问题

1. **"未找到客户端密钥"错误**
   - 检查环境变量名称是否正确
   - 确认环境变量已在运行环境中设置

2. **客户端身份验证失败**
   - 验证客户端使用的密钥与环境变量中的密钥一致
   - 检查密钥中是否包含额外的空格或特殊字符

3. **服务启动失败**
   - 检查配置文件格式是否正确
   - 确认所有必需的依赖服务已注册

## 向后兼容性

当前实现保持了向后兼容性：

1. 优先从环境变量读取密钥
2. 如果环境变量不存在，会从配置文件读取
3. 会记录警告日志提醒迁移

建议尽快完成迁移，未来版本可能会移除配置文件密钥支持。