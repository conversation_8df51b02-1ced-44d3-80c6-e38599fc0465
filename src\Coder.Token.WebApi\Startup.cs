﻿using System;
using System.Configuration;
using System.IO;
using System.Text.RegularExpressions;
using Coder.Authentication;
using Coder.ConsulHelper;
using Coder.DataInitial;
using Coder.HealthChecks;
using Coder.Token.Configuration;
using Coder.Token.HttpClients;
using Coder.Token.Interfaces;
using Coder.Token.WebApi.Data;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;

namespace Coder.Token.WebApi;

/// <summary>
/// 启动配置类，用于配置应用程序的服务和中间件
/// </summary>
public class Startup
{
    /// <summary>
    /// 本地开发环境的跨域请求正则表达式
    /// </summary>
    private static readonly Regex OriginWithRegex = new("http://localhost:*?", RegexOptions.IgnoreCase);

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">配置接口实例</param>
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    /// <summary>
    /// 应用程序配置属性
    /// </summary>
    public IConfiguration Configuration { get; }

    /// <summary>
    /// 配置应用程序服务
    /// 此方法由运行时调用，用于向DI容器添加服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 添加基础服务
        services.AddMemoryCache().AddResponseCaching();
        services.AddHealthChecks();
        services.AddHttpClient();
        services.AddControllers();
        
        // 配置Swagger文档
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "Coder.TokenService.WebApi", Version = "v1" });
        });
        
        // 配置Token服务选项
        services.Configure<TokenServiceConfiguration>(Configuration.GetSection("TokenService"));
        
        // 注册核心服务
        services.AddSingleton<ISecretKeyProvider, EnvironmentSecretKeyProvider>();
        services.AddSingleton<IRsaManager, RsaManager>();
        
        // 添加客户端初始化服务
        services.AddDataInitializer<ClientInit>();
        
        // 配置Redis缓存
        services.AddStackExchangeRedisCache(action =>
        {
            action.Configuration = Configuration.GetConnectionString("redis");
        });
        
        // 配置Consul服务发现
        ConsulSetup(services);
        
        // 配置Token服务
        TokenServiceSetup(services);
        
        // 配置数据库上下文
        OnConfigDbContext(services);

        // 配置CORS策略
        services.AddCors(builder =>
        {
            builder.AddDefaultPolicy(opt =>
            {
                opt.AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials()
                    .SetIsOriginAllowed(origin => OriginWithRegex.IsMatch(origin));
            });
        });
    }

    /// <summary>
    /// 配置HTTP请求管道
    /// 此方法由运行时调用，用于配置HTTP请求处理管道
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    /// <param name="env">Web主机环境</param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // 开发环境配置
        if (env.IsDevelopment() || env.IsStaging())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Coder.TokenService.WebApi v1"));
        }

        // 配置中间件管道
        app.UseCors();
        app.UseRouting();
        app.UseAuthentication().UseResponseCaching();
        app.UseAuthorization();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapCoderHealthChecks();
            endpoints.MapControllers();
        });
    }

    /// <summary>
    /// 配置Token服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <exception cref="ConfigurationErrorsException">配置错误异常</exception>
    private void TokenServiceSetup(IServiceCollection services)
    {
        services.AddTokenService(opt =>
        {
            Configuration.GetSection("TokenService").Bind(opt);
            opt.AddEfStores<ApplicationDbContext>();

            // 从环境变量获取票据过期时间和最大使用次数
            var expireMinutes = Environment.GetEnvironmentVariable("TicketExpireMinutes") ?? "3";
            opt.TicketExpireMinutes = Convert.ToInt32(expireMinutes);
            var maxTicketUseTimes = Environment.GetEnvironmentVariable("MaxTicketUseTimes") ?? "3";
            opt.MaxTicketUseTimes = Convert.ToInt32(maxTicketUseTimes);
        });
        
        // 配置JWT认证服务（非设计时）
        if (!EF.IsDesignTime)
            services.AddCoderJwtAuth(option =>
            {
                Configuration.GetSection("TokenService").Bind(option);
                option.IssuerSigningKey = (new RsaManager(null)).GetPrivateKeyPem();
            });
    }

    /// <summary>
    /// 配置Consul服务注册
    /// </summary>
    /// <param name="services">服务集合</param>
    protected virtual void ConsulSetup(IServiceCollection services)
    {
        services.RegisterToConsul(opt =>
        {
            Configuration.GetSection("ConsulOption").Bind(opt);
            var serviceId = Environment.GetEnvironmentVariable("SERVICE_ID");
            if (!string.IsNullOrEmpty(serviceId)) opt.ServiceId = serviceId;
        });
    }

    /// <summary>
    /// 配置数据库上下文
    /// </summary>
    /// <param name="services">服务集合</param>
    protected virtual void OnConfigDbContext(IServiceCollection services)
    {
        // 获取数据库连接信息
        var connection = Environment.GetEnvironmentVariable("DB_CONNECTION")?.Trim();
        var databaseType = Environment.GetEnvironmentVariable("DB_TYPE")?.Trim() ??
                          Configuration["DB_TYPE"] ?? "SQLITE";

        // 如果没有直接提供连接字符串，则从环境变量构建
        if (string.IsNullOrEmpty(connection))
        {
            connection = null;
            var dbHost = Environment.GetEnvironmentVariable("DB_HOST")?.Trim();
            if (dbHost != null)
            {
                var dbPassword = Environment.GetEnvironmentVariable("DB_PASSWORD") ?? "";
                var dbName = Environment.GetEnvironmentVariable("DB_DATABASE");
                if (string.IsNullOrEmpty(dbName))
                    dbName = Environment.GetEnvironmentVariable("DB_DEFAULT_DATABASE");
                if (string.IsNullOrEmpty(dbName))
                    dbName = "coder_token";

                var dbUser = Environment.GetEnvironmentVariable("DB_USER");
                var dbPort = Environment.GetEnvironmentVariable("DB_PORT")?.Trim();

                if (dbUser == null) throw new Exception("请输入DBUser值");

                // 根据数据库类型构建连接字符串
                if (databaseType != "DM")
                {
                    var port = string.IsNullOrEmpty(dbPort) ? "" : $";port={dbPort}";
                    connection = $"server={dbHost};database={dbName}{port};user={dbUser};password={dbPassword};";
                }
                else
                {
                    var port = string.IsNullOrEmpty(dbPort) ? "" : $":{dbPort}";
                    connection =
                        $"server={dbHost}{port};database={dbName};user={dbUser};password={dbPassword};convertToTz=True";
                }
            }
        }

        // 根据数据库类型设置具体连接字符串
        switch (databaseType)
        {
            case "MSSQL":
                connection ??= Configuration.GetConnectionString("mssql");
                if (!connection.Contains("TrustServerCertificate=true")) connection += "TrustServerCertificate=true";
                break;
            case "SQLITE":
                if (!Directory.Exists("_data")) Directory.CreateDirectory("_data");
                connection = "Data Source=database.db;";
                break;
            case "MYSQL":
                connection ??= Configuration.GetConnectionString("mysql");
                break;
            case "DM":
                connection ??= Configuration.GetConnectionString("dm");
                break;
        }

        // 记录数据库配置日志（隐藏敏感信息）
        var connectionInfo = connection?.Contains("password=", StringComparison.OrdinalIgnoreCase) == true
            ? $"{databaseType}: [连接字符串包含密码，已隐藏]"
            : $"{databaseType}: {connection}";
        
        var logger = services.BuildServiceProvider().GetService<ILogger<Startup>>();
        logger?.LogInformation("配置数据库连接: {ConnectionInfo}", connectionInfo);

        // 配置DbContext
        services.AddDbContext<ApplicationDbContext>(
            options1 =>
            {
                options1.UseLazyLoadingProxies();
                switch (databaseType)
                {
                    case "MSSQL":
                        options1.UseSqlServer(connection, action =>
                        {
                            action.CommandTimeout(300);
                            action.MigrationsAssembly("Coder.Token.Migrations.Mssql");
                        });
                        break;
                    case "SQLITE":
                        options1.UseSqlite(connection, action =>
                        {
                            action.CommandTimeout(300);
                            action.MigrationsAssembly("Coder.Token.Migrations.Sqlite");
                        });
                        break;
                    case "MYSQL":
                        options1.UseMySql(connection, new MySqlServerVersion(new Version(5, 7, 25)), action =>
                        {
                            action.CommandTimeout(300);
                            action.MigrationsAssembly("Coder.Token.Migrations.Mysql");
                        });
                        break;
                    case "DM":
                        options1.UseDm(connection, action =>
                        {
                            action.CommandTimeout(int.MaxValue);
                            action.MigrationsAssembly("Coder.Token.Migrations.DM");
                        });
                        break;
                    default:
                        throw new Exception("Not support " + databaseType);
                }
            });
    }
}