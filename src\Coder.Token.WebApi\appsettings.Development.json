﻿{
  "DB_TYPE": "MYSQL",
  "ConnectionStrings": {
    "dm": "server=************:30236;database=storage;user=STORAGE;password=*********;",
    "mysql": "server=*************;database=oa2;user=root;password=*********;CharSet=utf8;",
    "mssql": "Server=************;Database=storage_1.0;password=**********;user=sa;",
    "redis": "localhost:6379,defaultDatabase=9"
  },
  "NLog": {
    "LokiHost": "http://localhost:3100" //docker 的名字
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Debug",
      "Microsoft.Hosting.Lifetime": "Debug"
    }
  },
  "ConsulOption": {
    "ConsulServer": "http://127.0.0.1:8500",
    "ServiceHost": "_HOST_", /* 要注册的服务 url */
    "ServicePort": -1, /* 要注册服务的访问端口 */
    "ServiceName": "coder_token", /* 服务名称，集群的时候采用这个名称来获取服务 */
    "ServiceId": "coder_token-DEV", /* 服务id 必须唯一，用于记录那个服务提供者出现问题 */
    "Tags": [ "token", "登录服务" ], /* 可选 日志*/
    "HealthCheckUrl": "http://_HOST_:_PORT_/Health/Status",
    "Enable": false
  }
}
