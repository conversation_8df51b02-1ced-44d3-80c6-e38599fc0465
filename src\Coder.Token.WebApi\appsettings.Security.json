{"TokenService": {"Issuer": "Coder.Token.Service", "ExpireMinutes": 30, "TicketExpireMinutes": 3, "MaxTicketUseTimes": 3, "Clients": {"Member": {"Name": "Member", "Description": "会员系统", "Services": ["Token"], "ClaimBuildType": "Both"}, "User": {"Name": "User", "Description": "用户", "Services": ["Token"], "ClaimBuildType": "Customs"}, "Gateway": {"Name": "Gateway", "Description": "网关", "Services": ["Token"], "ClaimBuildType": "Client"}, "OTP": {"Name": "OTP", "Description": "一次性密码", "Services": ["Token", "Member"], "ClaimBuildType": "Client"}}}, "ClientSecrets": {"Member": "从环境变量 CLIENT_SECRET_MEMBER 获取", "User": "从环境变量 CLIENT_SECRET_USER 获取", "Gateway": "从环境变量 CLIENT_SECRET_GATEWAY 获取", "OTP": "从环境变量 CLIENT_SECRET_OTP 获取"}}