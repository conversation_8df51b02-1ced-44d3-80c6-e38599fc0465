﻿{
  "ConnectionStrings": {
    "dm": "server=************:30236;database=storage;user=STORAGE;password=*********;",
    "mysql": "server=mysql;port=3306;database=coder_tokens;user=root;password=*********;CharSet=utf8;",
    "mssql": "Server=localhost\\SQLEXPRESS;Database=zhc_storage;Trusted_Connection=True;",
    "redis": "redis:6379,defaultDatabase=9"

  },
  "DB_TYPE": "DM",
  "TokenService": {
    "ExpireMinutes": 1440,
    "Issuer": "Coder-Token-Service",
    "UserAudience": "业务系统名称",
    "Host": "http://gateway:8080/token",
    "SecretKey": "123467",
    "Client": "Token"
  },
  "NLog": {
 
    "LokiHost": "http://loki:3100" //docker 的名字
  },

  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Coder": "Information"
    }
  },
  "ConsulOption": {
    "ConsulServer": "http://consul:8500",
    "ServiceHost": "_HOST_", /* 要注册的服务 url */
    "ServicePort": -1, /* 要注册服务的访问端口 */
    "ServiceName": "coder_token", /* 服务名称，集群的时候采用这个名称来获取服务 */
    "ServiceId": "coder_token-*", /* 服务id 必须唯一，用于记录那个服务提供者出现问题 , 也可以env serviceId 设置*/
    "Tags": [ "token", "登录服务" ], /* 可选 日志*/
    "HealthCheckUrl": "http://_HOST_:_PORT_/Health/Status"
  },
  "AllowedHosts": "*"
}
