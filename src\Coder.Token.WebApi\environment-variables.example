# 客户端密钥配置示例 - Environment Variables Example
# 请将这些环境变量设置到您的系统中
# Please set these environment variables in your system

# 会员系统密钥
CLIENT_SECRET_MEMBER=your-very-secure-member-secret-key-here

# 用户密钥
CLIENT_SECRET_USER=your-very-secure-user-secret-key-here

# 网关密钥
CLIENT_SECRET_GATEWAY=your-very-secure-gateway-secret-key-here

# OTP密钥
CLIENT_SECRET_OTP=your-very-secure-otp-secret-key-here

# 微信密钥
CLIENT_SECRET_WECHAT=your-very-secure-wechat-secret-key-here

# 工作流密钥
CLIENT_SECRET_SWF=your-very-secure-swf-secret-key-here

# 文件系统密钥
CLIENT_SECRET_FS=your-very-secure-fs-secret-key-here

# 通知系统密钥
CLIENT_SECRET_NOTIFY=your-very-secure-notify-secret-key-here

# 组织密钥
CLIENT_SECRET_ORG=your-very-secure-org-secret-key-here

# 数据库配置
DB_TYPE=SQLITE
DB_CONNECTION=
# 如果使用外部数据库，可以设置以下参数
# DB_HOST=localhost
# DB_PORT=3306
# DB_USER=username
# DB_PASSWORD=password
# DB_DATABASE=coder_token

# Redis配置
REDIS_CONNECTION=localhost:6379

# 服务配置
SERVICE_ID=token-service-001
TICKET_EXPIRE_MINUTES=3
MAX_TICKET_USE_TIMES=3