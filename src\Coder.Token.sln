﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32519.379
MinimumVisualStudioVersion = 15.0.26124.0
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Core", "Coder.Token.Core\Coder.Token.Core.csproj", "{4DDBE799-DD86-4003-B211-C8A9C3CECC27}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Stores.Ef", "Coder.Token.Stores.Ef\Coder.Token.Stores.Ef.csproj", "{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.HttpClients", "Coder.Token.HttpClients\Coder.Token.HttpClients.csproj", "{C29740FA-A925-40AC-BA01-AD656CBF7B87}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Abstractions", "Coder.Token.Abstractions\Coder.Token.Abstractions.csproj", "{ADD230E8-1213-4791-9EB4-822EE1E47B59}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Authentication", "Authentication", "{9C4D909F-534B-4A5F-8286-DD93D63A8C59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Authentication", "Coder.Authentication\Coder.Authentication.csproj", "{F858F15E-9DF2-4177-9DAD-B1788AB5D732}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Build", "Build", "{BA880F04-554F-4670-BF1F-3550ACF6E6A2}"
	ProjectSection(SolutionItems) = preProject
		..\.gitlab-ci.yml = ..\.gitlab-ci.yml
		build-migration.ps1 = build-migration.ps1
		..\docker\token\build.ps1 = ..\docker\token\build.ps1
		nuget.config = nuget.config
		..\publish-to.nuget.ps1 = ..\publish-to.nuget.ps1
		..\README.md = ..\README.md
		remove-migrations.ps1 = remove-migrations.ps1
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Migrations", "Migrations", "{225C7A8A-77F1-4E1E-8420-1124A70BDDC6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Migrations.Mysql", "Coder.Token.Migrations.Mysql\Coder.Token.Migrations.Mysql.csproj", "{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Migrations.Mssql", "Coder.Token.Migrations.Mssql\Coder.Token.Migrations.Mssql.csproj", "{734A71C4-FD69-48FE-BD36-497848A2DA72}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Migrations.Sqlite", "Coder.Token.Migrations.Sqlite\Coder.Token.Migrations.Sqlite.csproj", "{64C0F394-BD7F-4C49-8B73-3367413A91EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.WebApi", "Coder.Token.WebApi\Coder.Token.WebApi.csproj", "{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.Migrations.DM", "Coder.Token.Migrations.DM\Coder.Token.Migrations.DM.csproj", "{F66091AA-5ACD-4603-B106-02674A0E2CC6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Coder.Token.UnitTest", "Coder.Token.UnitTest\Coder.Token.UnitTest.csproj", "{3B3277FA-666B-4714-95A5-2C32916B8D6A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Debug|x64.Build.0 = Debug|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Debug|x86.Build.0 = Debug|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Release|x64.ActiveCfg = Release|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Release|x64.Build.0 = Release|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Release|x86.ActiveCfg = Release|Any CPU
		{4DDBE799-DD86-4003-B211-C8A9C3CECC27}.Release|x86.Build.0 = Release|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Debug|x64.Build.0 = Debug|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Debug|x86.Build.0 = Debug|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Release|x64.ActiveCfg = Release|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Release|x64.Build.0 = Release|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Release|x86.ActiveCfg = Release|Any CPU
		{39D5A2A7-0B13-495B-9AD2-9B797DEAADF0}.Release|x86.Build.0 = Release|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Debug|x64.Build.0 = Debug|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Debug|x86.Build.0 = Debug|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Release|Any CPU.Build.0 = Release|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Release|x64.ActiveCfg = Release|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Release|x64.Build.0 = Release|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Release|x86.ActiveCfg = Release|Any CPU
		{C29740FA-A925-40AC-BA01-AD656CBF7B87}.Release|x86.Build.0 = Release|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Debug|x64.Build.0 = Debug|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Debug|x86.Build.0 = Debug|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Release|x64.ActiveCfg = Release|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Release|x64.Build.0 = Release|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Release|x86.ActiveCfg = Release|Any CPU
		{ADD230E8-1213-4791-9EB4-822EE1E47B59}.Release|x86.Build.0 = Release|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Debug|x64.Build.0 = Debug|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Debug|x86.Build.0 = Debug|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Release|Any CPU.Build.0 = Release|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Release|x64.ActiveCfg = Release|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Release|x64.Build.0 = Release|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Release|x86.ActiveCfg = Release|Any CPU
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732}.Release|x86.Build.0 = Release|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Debug|x64.Build.0 = Debug|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Debug|x86.Build.0 = Debug|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Release|x64.ActiveCfg = Release|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Release|x64.Build.0 = Release|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Release|x86.ActiveCfg = Release|Any CPU
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D}.Release|x86.Build.0 = Release|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Debug|x64.ActiveCfg = Debug|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Debug|x64.Build.0 = Debug|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Debug|x86.ActiveCfg = Debug|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Debug|x86.Build.0 = Debug|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Release|Any CPU.Build.0 = Release|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Release|x64.ActiveCfg = Release|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Release|x64.Build.0 = Release|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Release|x86.ActiveCfg = Release|Any CPU
		{734A71C4-FD69-48FE-BD36-497848A2DA72}.Release|x86.Build.0 = Release|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Debug|x64.Build.0 = Debug|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Debug|x86.Build.0 = Debug|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Release|x64.ActiveCfg = Release|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Release|x64.Build.0 = Release|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Release|x86.ActiveCfg = Release|Any CPU
		{64C0F394-BD7F-4C49-8B73-3367413A91EB}.Release|x86.Build.0 = Release|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Debug|x64.Build.0 = Debug|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Debug|x86.Build.0 = Debug|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Release|Any CPU.Build.0 = Release|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Release|x64.ActiveCfg = Release|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Release|x64.Build.0 = Release|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Release|x86.ActiveCfg = Release|Any CPU
		{7BD35AFC-7540-4C00-ADC9-0F5941C28ABF}.Release|x86.Build.0 = Release|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Debug|x64.Build.0 = Debug|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Debug|x86.Build.0 = Debug|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Release|Any CPU.Build.0 = Release|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Release|x64.ActiveCfg = Release|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Release|x64.Build.0 = Release|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Release|x86.ActiveCfg = Release|Any CPU
		{F66091AA-5ACD-4603-B106-02674A0E2CC6}.Release|x86.Build.0 = Release|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Debug|x64.Build.0 = Debug|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Debug|x86.Build.0 = Debug|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Release|x64.ActiveCfg = Release|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Release|x64.Build.0 = Release|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Release|x86.ActiveCfg = Release|Any CPU
		{3B3277FA-666B-4714-95A5-2C32916B8D6A}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F858F15E-9DF2-4177-9DAD-B1788AB5D732} = {9C4D909F-534B-4A5F-8286-DD93D63A8C59}
		{DD4F0CDC-1CC6-4203-A96A-64FCA8857E9D} = {225C7A8A-77F1-4E1E-8420-1124A70BDDC6}
		{734A71C4-FD69-48FE-BD36-497848A2DA72} = {225C7A8A-77F1-4E1E-8420-1124A70BDDC6}
		{64C0F394-BD7F-4C49-8B73-3367413A91EB} = {225C7A8A-77F1-4E1E-8420-1124A70BDDC6}
		{F66091AA-5ACD-4603-B106-02674A0E2CC6} = {225C7A8A-77F1-4E1E-8420-1124A70BDDC6}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {576573D2-57B8-491B-9EE3-477523020CC7}
	EndGlobalSection
EndGlobal
