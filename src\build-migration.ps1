cd Coder.Token.WebApi


$name = Read-Host "Please enter migration name."
 

$env:DB_TYPE="MYSQL"
dotnet ef migrations add ${name} --project ../Coder.Token.Migrations.Mysql

$env:DB_TYPE="MSSQL"
dotnet ef migrations add ${name} --project ../Coder.Token.Migrations.Mssql

$env:DB_TYPE="SQLITE"
dotnet ef migrations add ${name} --project ../Coder.Token.Migrations.Sqlite

$env:DB_TYPE="DM"
dotnet ef migrations add ${name} --project ../Coder.Token.Migrations.DM